"""
余额获取重试服务
提供可靠的卡片余额获取功能，包含重试机制和错误处理
"""

import asyncio
import logging
from typing import Dict, Any, Optional, Tuple
from sqlalchemy.orm import Session

from app.core.walmart_api import WalmartAPI
from app.models.walmart_ck import WalmartCK
from app.utils.time_utils import get_current_time
logger = logging.getLogger(__name__)

# 重试配置
MAX_BALANCE_RETRIES = 3  # 最大重试次数（总共4次尝试）
RETRY_DELAYS = [1, 2, 4]  # 重试间隔（秒）
BALANCE_TIMEOUT = 10  # 单次API调用超时时间（秒）


class BalanceRetryService:
    """余额获取重试服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def get_card_balance_with_retry(
        self,
        record,
        walmart_ck_id: int,
        trace_id: Optional[str] = None
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        带重试机制的余额获取
        
        Args:
            record: 卡记录对象
            walmart_ck_id: 沃尔玛CK ID
            trace_id: 追踪ID
            
        Returns:
            Tuple[bool, Dict[str, Any]]: (是否成功, 结果数据)
            结果数据格式：
            {
                "balance": float,
                "cardBalance": float, 
                "balanceCnt": int,
                "error_message": str,  # 仅失败时
                "retry_count": int,    # 实际重试次数
                "total_attempts": int  # 总尝试次数
            }
        """
        card_number = record.card_number
        record_id = record.id
        
        logger.info(
            f"[BALANCE_RETRY_START] 开始获取卡余额 | "
            f"record_id={record_id} | card_number={card_number[:6]}*** | "
            f"walmart_ck_id={walmart_ck_id} | trace_id={trace_id}"
        )
        
        # 获取沃尔玛CK配置
        try:
            walmart_ck = self._get_walmart_ck_config(walmart_ck_id, record_id)
            if not walmart_ck:
                return False, {
                    "error_message": f"沃尔玛CK配置不存在: {walmart_ck_id}",
                    "retry_count": 0,
                    "total_attempts": 1
                }
        except Exception as e:
            logger.error(f"[BALANCE_CK_ERROR] 获取CK配置失败 | record_id={record_id} | error={e}")
            return False, {
                "error_message": f"获取CK配置失败: {str(e)}",
                "retry_count": 0,
                "total_attempts": 1
            }
        
        # 创建API实例
        try:
            walmart_api = self._create_walmart_api(walmart_ck, record_id)
        except Exception as e:
            logger.error(f"[BALANCE_API_ERROR] 创建API实例失败 | record_id={record_id} | error={e}")
            return False, {
                "error_message": f"创建API实例失败: {str(e)}",
                "retry_count": 0,
                "total_attempts": 1
            }
        
        # 执行重试逻辑
        last_error = None
        for attempt in range(MAX_BALANCE_RETRIES + 1):  # 总共4次尝试
            try:
                logger.info(
                    f"[BALANCE_ATTEMPT] 尝试获取余额 #{attempt + 1} | "
                    f"record_id={record_id} | card_number={card_number[:6]}***"
                )
                
                # 调用余额获取API
                balance_data = await self._fetch_balance_with_timeout(
                    walmart_api, card_number, record_id
                )
                
                if balance_data:
                    # 成功获取余额
                    logger.info(
                        f"[BALANCE_SUCCESS] 余额获取成功 | record_id={record_id} | "
                        f"attempt={attempt + 1} | balance={balance_data.get('balance')} | "
                        f"cardBalance={balance_data.get('cardBalance')} | "
                        f"balanceCnt={balance_data.get('balanceCnt')}"
                    )
                    
                    result = {
                        "balance": balance_data.get("balance"),
                        "cardBalance": balance_data.get("cardBalance"),
                        "balanceCnt": balance_data.get("balanceCnt"),
                        "retry_count": attempt,
                        "total_attempts": attempt + 1
                    }
                    return True, result
                else:
                    # API返回空数据
                    last_error = "API返回空余额数据"
                    logger.warning(
                        f"[BALANCE_EMPTY] API返回空数据 | record_id={record_id} | attempt={attempt + 1}"
                    )
                    
            except Exception as e:
                last_error = str(e)
                logger.warning(
                    f"[BALANCE_ATTEMPT_FAILED] 余额获取尝试失败 | "
                    f"record_id={record_id} | attempt={attempt + 1} | error={e}"
                )
            
            # 如果不是最后一次尝试，等待后重试
            if attempt < MAX_BALANCE_RETRIES:
                delay = RETRY_DELAYS[attempt] if attempt < len(RETRY_DELAYS) else RETRY_DELAYS[-1]
                logger.info(
                    f"[BALANCE_RETRY_WAIT] 等待 {delay} 秒后重试 | "
                    f"record_id={record_id} | next_attempt={attempt + 2}"
                )
                await asyncio.sleep(delay)
        
        # 所有重试都失败
        error_message = f"余额获取失败，已重试{MAX_BALANCE_RETRIES}次: {last_error}"
        logger.error(
            f"[BALANCE_ALL_FAILED] 所有余额获取尝试都失败 | "
            f"record_id={record_id} | total_attempts={MAX_BALANCE_RETRIES + 1} | "
            f"final_error={last_error}"
        )
        
        return False, {
            "error_message": error_message,
            "retry_count": MAX_BALANCE_RETRIES,
            "total_attempts": MAX_BALANCE_RETRIES + 1
        }
    
    def _get_walmart_ck_config(self, walmart_ck_id: int, record_id: int) -> Optional[WalmartCK]:
        """获取沃尔玛CK配置"""
        try:
            # 检查是否为异步会话
            from sqlalchemy.ext.asyncio import AsyncSession
            if isinstance(self.db, AsyncSession):
                logger.error(f"[BALANCE_CK_ASYNC_ERROR] 余额服务不支持异步会话 | walmart_ck_id={walmart_ck_id} | record_id={record_id}")
                return None

            walmart_ck = self.db.query(WalmartCK).filter(WalmartCK.id == walmart_ck_id).first()
            if not walmart_ck:
                logger.error(f"[BALANCE_CK_NOT_FOUND] CK配置不存在 | walmart_ck_id={walmart_ck_id} | record_id={record_id}")
                return None

            if not walmart_ck.active:
                logger.warning(f"[BALANCE_CK_INACTIVE] CK已禁用 | walmart_ck_id={walmart_ck_id} | record_id={record_id}")
                # 注意：即使CK被禁用，仍然可以用来查询余额

            return walmart_ck
        except Exception as e:
            logger.error(f"[BALANCE_CK_QUERY_ERROR] 查询CK配置异常 | walmart_ck_id={walmart_ck_id} | record_id={record_id} | error={e}")
            return None
    
    def _prepare_api_request_params(self, walmart_ck: WalmartCK) -> Optional[Dict[str, Any]]:
        """准备API请求参数"""
        user_sign_raw = walmart_ck.sign
        sign_parts = user_sign_raw.split("#")
        if len(sign_parts) < 3:
            logger.error(f"沃尔玛CK {walmart_ck.id} 签名格式错误: {user_sign_raw}")
            return None

        return {
            "sign": sign_parts[0],
            "encryption_key": sign_parts[1],
            "version": sign_parts[2],
        }

    def _create_walmart_api(self, walmart_ck: WalmartCK, record_id: int) -> WalmartAPI:
        """创建沃尔玛API实例"""
        try:
            api_params = self._prepare_api_request_params(walmart_ck)
            if not api_params:
                logger.error(
                    f"[BALANCE_ERROR] 准备API参数失败 | "
                    f"record_id={record_id} | walmart_ck_id={walmart_ck.id}"
                )
                raise ValueError("准备API参数失败")

            # 使用遗留方法创建API实例以保持兼容性
            walmart_api = WalmartAPI.create_legacy(
                base_url="https://apicard.swiftpass.cn",
                encryption_key=api_params["encryption_key"],
                token=None,
                version=api_params["version"],
                sign=api_params["sign"]
            )
            logger.debug(f"[BALANCE_API_CREATED] API实例创建成功 | record_id={record_id} | ck_id={walmart_ck.id}")
            return walmart_api
        except Exception as e:
            logger.error(f"[BALANCE_API_CREATE_ERROR] API实例创建失败 | record_id={record_id} | ck_id={walmart_ck.id} | error={e}")
            raise
    
    async def _fetch_balance_with_timeout(
        self, 
        walmart_api: WalmartAPI, 
        card_number: str, 
        record_id: int
    ) -> Optional[Dict[str, Any]]:
        """
        带超时的余额获取
        
        Args:
            walmart_api: API实例
            card_number: 卡号
            record_id: 记录ID
            
        Returns:
            Optional[Dict[str, Any]]: 余额数据或None
        """
        try:
            # 使用asyncio.wait_for实现超时控制
            balance_data = await asyncio.wait_for(
                self._async_get_balance(walmart_api, card_number),
                timeout=BALANCE_TIMEOUT
            )
            return balance_data
        except asyncio.TimeoutError:
            logger.warning(f"[BALANCE_TIMEOUT] 余额获取超时 | record_id={record_id} | timeout={BALANCE_TIMEOUT}s")
            raise Exception(f"余额获取超时（{BALANCE_TIMEOUT}秒）")
        except Exception as e:
            logger.error(f"[BALANCE_FETCH_ERROR] 余额获取异常 | record_id={record_id} | error={e}")
            raise
    
    async def _async_get_balance(self, walmart_api: WalmartAPI, card_number: str) -> Dict[str, Any]:
        """
        异步获取余额（包装同步API）
        """
        # 在线程池中执行同步API调用
        loop = asyncio.get_event_loop()
        balance_data = await loop.run_in_executor(
            None, 
            walmart_api.get_card_balance_sync, 
            card_number
        )
        return balance_data
