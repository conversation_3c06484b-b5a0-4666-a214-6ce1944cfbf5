package walmart

import (
	"context"
	"fmt"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"walmart-bind-card-processor/internal/model"
)

// ConfigManager 沃尔玛API配置管理器
type ConfigManager struct {
	db     *gorm.DB
	logger *logrus.Logger
}

// NewConfigManager 创建配置管理器
func NewConfigManager(db *gorm.DB, logger *logrus.Logger) *ConfigManager {
	return &ConfigManager{
		db:     db,
		logger: logger,
	}
}

// GetAPIBaseURL 获取API基础URL
func (cm *ConfigManager) GetAPIBaseURL(ctx context.Context) (string, error) {
	var server model.WalmartServer
	
	// 查询活跃的服务器配置
	err := cm.db.WithContext(ctx).
		Where("is_active = ? AND maintenance_mode = ?", true, false).
		First(&server).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			cm.logger.Warn("No active Walmart server configuration found")
			return "", fmt.Errorf("no active Walmart server configuration found")
		}
		cm.logger.WithError(err).Error("Failed to get Walmart server configuration")
		return "", fmt.Errorf("failed to get Walmart server configuration: %w", err)
	}

	if server.APIURL == "" {
		cm.logger.WithField("server_id", server.ID).Warn("Walmart server API URL is empty")
		return "", fmt.Errorf("Walmart server API URL is empty")
	}

	cm.logger.WithFields(logrus.Fields{
		"api_url":   server.APIURL,
		"server_id": server.ID,
	}).Debug("Retrieved Walmart API base URL")

	return server.APIURL, nil
}

// GetServerConfig 获取完整的服务器配置
func (cm *ConfigManager) GetServerConfig(ctx context.Context) (*model.WalmartServer, error) {
	var server model.WalmartServer
	
	err := cm.db.WithContext(ctx).
		Where("is_active = ? AND maintenance_mode = ?", true, false).
		First(&server).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("no active Walmart server configuration found")
		}
		return nil, fmt.Errorf("failed to get Walmart server configuration: %w", err)
	}

	return &server, nil
}

// CreateDefaultConfig 创建默认配置（如果不存在）
func (cm *ConfigManager) CreateDefaultConfig(ctx context.Context, apiURL string) error {
	// 检查是否已存在配置
	var count int64
	err := cm.db.WithContext(ctx).Model(&model.WalmartServer{}).Count(&count).Error
	if err != nil {
		return fmt.Errorf("failed to check existing configuration: %w", err)
	}

	if count > 0 {
		cm.logger.Info("Walmart server configuration already exists, skipping creation")
		return nil
	}

	// 创建默认配置
	defaultConfig := model.WalmartServer{
		APIURL:                  apiURL,
		Referer:                 "",
		Timeout:                 30,
		RetryCount:              3,
		DailyBindLimit:          1000,
		APIRateLimit:            60,
		MaxRetryTimes:           3,
		BindTimeoutSeconds:      30,
		VerificationCodeExpires: 300,
		LogRetentionDays:        90,
		EnableIPWhitelist:       true,
		EnableSecurityAudit:     true,
		MaintenanceMode:         false,
		IsActive:                true,
		ExtraConfig:             "{}",
	}

	err = cm.db.WithContext(ctx).Create(&defaultConfig).Error
	if err != nil {
		return fmt.Errorf("failed to create default configuration: %w", err)
	}

	cm.logger.WithFields(logrus.Fields{
		"api_url":   apiURL,
		"config_id": defaultConfig.ID,
	}).Info("Created default Walmart server configuration")

	return nil
}

// IsMaintenanceMode 检查是否处于维护模式
func (cm *ConfigManager) IsMaintenanceMode(ctx context.Context) (bool, error) {
	var server model.WalmartServer
	
	err := cm.db.WithContext(ctx).
		Select("maintenance_mode").
		Where("is_active = ?", true).
		First(&server).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 如果没有配置，默认不是维护模式
			return false, nil
		}
		return false, fmt.Errorf("failed to check maintenance mode: %w", err)
	}

	return server.MaintenanceMode, nil
}
