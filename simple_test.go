package main

import (
	"fmt"
	"time"
	"walmart-bind-card-processor/internal/services"
	"github.com/google/uuid"
)

func main() {
	fmt.Println("🧪 UUID生成测试 - 验证修复效果")
	fmt.Println("================================")

	// 测试UUID生成的唯一性
	const testCount = 1000
	uuidMap := make(map[string]bool)
	duplicates := 0

	fmt.Printf("生成 %d 个UUID进行唯一性测试...\n", testCount)
	
	start := time.Now()
	
	for i := 0; i < testCount; i++ {
		// 使用标准UUID库
		id := uuid.New().String()
		
		if uuidMap[id] {
			duplicates++
			fmt.Printf("❌ 发现重复UUID: %s\n", id)
		} else {
			uuidMap[id] = true
		}
	}
	
	duration := time.Since(start)
	
	fmt.Printf("\n📊 测试结果:\n")
	fmt.Printf("总生成数: %d\n", testCount)
	fmt.Printf("重复数量: %d\n", duplicates)
	fmt.Printf("唯一数量: %d\n", len(uuidMap))
	fmt.Printf("生成耗时: %v\n", duration)
	fmt.Printf("平均耗时: %v/个\n", duration/time.Duration(testCount))
	
	if duplicates == 0 {
		fmt.Println("✅ UUID生成测试通过! 无重复UUID")
	} else {
		fmt.Printf("❌ UUID生成测试失败! 发现 %d 个重复UUID\n", duplicates)
	}

	// 测试高并发UUID生成
	fmt.Println("\n🚀 高并发UUID生成测试...")
	testConcurrentUUID()
}

func testConcurrentUUID() {
	const (
		goroutines = 100
		uuidsPerGoroutine = 100
	)
	
	uuidChan := make(chan string, goroutines*uuidsPerGoroutine)
	
	start := time.Now()
	
	// 启动多个goroutine并发生成UUID
	for i := 0; i < goroutines; i++ {
		go func() {
			for j := 0; j < uuidsPerGoroutine; j++ {
				uuidChan <- uuid.New().String()
			}
		}()
	}
	
	// 收集所有UUID
	uuidMap := make(map[string]bool)
	duplicates := 0
	
	for i := 0; i < goroutines*uuidsPerGoroutine; i++ {
		id := <-uuidChan
		if uuidMap[id] {
			duplicates++
			fmt.Printf("❌ 并发测试发现重复UUID: %s\n", id)
		} else {
			uuidMap[id] = true
		}
	}
	
	duration := time.Since(start)
	totalUUIDs := goroutines * uuidsPerGoroutine
	
	fmt.Printf("\n📊 并发测试结果:\n")
	fmt.Printf("并发数: %d goroutines\n", goroutines)
	fmt.Printf("每个goroutine生成: %d 个UUID\n", uuidsPerGoroutine)
	fmt.Printf("总生成数: %d\n", totalUUIDs)
	fmt.Printf("重复数量: %d\n", duplicates)
	fmt.Printf("唯一数量: %d\n", len(uuidMap))
	fmt.Printf("生成耗时: %v\n", duration)
	
	if duplicates == 0 {
		fmt.Println("✅ 高并发UUID生成测试通过! 无重复UUID")
	} else {
		fmt.Printf("❌ 高并发UUID生成测试失败! 发现 %d 个重复UUID\n", duplicates)
	}
}
