package services

import (
	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"walmart-bind-card-processor/internal/config"
)

// ServiceFactory 服务工厂，用于创建配置化的服务实例
type ServiceFactory struct {
	config *config.Config
	db     *gorm.DB
	redis  *redis.Client
	logger *zap.Logger
}

// NewServiceFactory 创建服务工厂
func NewServiceFactory(config *config.Config, db *gorm.DB, redis *redis.Client, logger *zap.Logger) *ServiceFactory {
	return &ServiceFactory{
		config: config,
		db:     db,
		redis:  redis,
		logger: logger,
	}
}

// CreateCKStatusSyncService 创建CK状态同步服务
func (f *ServiceFactory) CreateCKStatusSyncService() *CKStatusSyncService {
	return NewCKStatusSyncService(f.db, f.redis, f.logger, &f.config.CKManagement.StatusSync)
}

// CreateCKWeightManager 创建CK权重管理器
func (f *ServiceFactory) CreateCKWeightManager() *CKWeightManager {
	return NewCKWeightManager(f.db, f.redis, f.logger, &f.config.CKManagement.WeightAlgorithm)
}

// CreateCKPreoccupationManager 创建CK预占用管理器
func (f *ServiceFactory) CreateCKPreoccupationManager() *CKPreoccupationManager {
	return NewCKPreoccupationManager(f.db, f.redis, f.logger, &f.config.CKManagement.Preoccupation, &f.config.CKManagement.DistributedLock)
}

// CreateCKMonitoringService 创建CK监控服务
func (f *ServiceFactory) CreateCKMonitoringService() *CKMonitoringService {
	return NewCKMonitoringService(f.db, f.redis, f.logger, &f.config.CKManagement.Monitoring)
}
