#!/usr/bin/env python3
"""
原子性绑卡服务
确保绑卡流程的数据一致性和事务完整性
"""

import asyncio
import time
from typing import Dict, Any, Optional, Union
from contextlib import asynccontextmanager
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError

from app.core.logging import get_logger
from app.models.walmart_ck import WalmartCK
from app.models.card_record import CardRecord, CardStatus
from app.utils.time_utils import get_current_time

logger = get_logger("atomic_binding_service")


class AtomicBindingService:
    """原子性绑卡服务 - 确保数据一致性"""

    def __init__(self, db: Union[Session, AsyncSession]):
        self.db = db
        self.is_async = isinstance(db, AsyncSession)

    @asynccontextmanager
    async def atomic_transaction(self):
        """原子性事务上下文管理器"""
        try:
            # 开始事务
            yield self.db
            # 提交事务
            if self.is_async:
                await self.db.commit()
            else:
                self.db.commit()
            logger.debug("原子性事务提交成功")
        except Exception as e:
            # 回滚事务
            if self.is_async:
                await self.db.rollback()
            else:
                self.db.rollback()
            logger.error(f"原子性事务回滚: {e}")
            raise
    
    async def execute_atomic_binding(
        self,
        record_id: str,
        merchant_id: int,
        api_result: Dict[str, Any],
        is_success: bool
    ) -> bool:
        """
        执行原子性绑卡操作
        确保状态更新、CK信息保存、CK使用统计在同一个事务中
        
        Args:
            record_id: 绑卡记录ID
            merchant_id: 商户ID
            api_result: API调用结果
            is_success: 是否绑卡成功
            
        Returns:
            bool: 操作是否成功
        """
        try:
            async with self.atomic_transaction():
                # 1. 使用行锁获取记录，防止并发修改
                if self.is_async:
                    from sqlalchemy import select
                    stmt = select(CardRecord).filter(
                        CardRecord.id == record_id
                    ).with_for_update()
                    result = await self.db.execute(stmt)
                    record = result.scalar_one_or_none()
                else:
                    record = self.db.query(CardRecord).filter(
                        CardRecord.id == record_id
                    ).with_for_update().first()

                if not record:
                    logger.error(f"记录不存在: {record_id}")
                    return False
                
                # 2. 验证记录状态，防止重复处理
                if record.status not in ['pending', 'binding']:
                    logger.warning(f"记录状态异常，跳过处理: {record_id}, 当前状态: {record.status}")
                    return False
                
                # 3. 获取使用的CK信息
                walmart_ck_id = api_result.get('walmart_ck_id')
                if not walmart_ck_id:
                    logger.error(f"API结果中缺少CK信息: {record_id}")
                    return False
                
                # 4. 使用行锁获取CK，防止并发修改bind_count
                if self.is_async:
                    ck_stmt = select(WalmartCK).filter(
                        WalmartCK.id == walmart_ck_id,
                        WalmartCK.merchant_id == merchant_id
                    ).with_for_update()
                    ck_result = await self.db.execute(ck_stmt)
                    ck = ck_result.scalar_one_or_none()
                else:
                    ck = self.db.query(WalmartCK).filter(
                        WalmartCK.id == walmart_ck_id,
                        WalmartCK.merchant_id == merchant_id
                    ).with_for_update().first()

                if not ck:
                    logger.error(f"CK不存在或不属于该商户: {walmart_ck_id}, {merchant_id}")
                    return False
                
                # 5. 原子性更新所有相关数据
                if is_success:
                    # 绑卡成功的处理
                    await self._handle_success_atomic(record, ck, api_result)
                else:
                    # 绑卡失败的处理
                    await self._handle_failure_atomic(record, api_result)
                
                logger.info(f"原子性绑卡操作完成: {record_id}, 成功: {is_success}")
                return True
                
        except Exception as e:
            logger.error(f"原子性绑卡操作失败: {record_id}, 错误: {e}")
            return False
    
    async def _handle_success_atomic(
        self,
        record: CardRecord,
        ck: WalmartCK,
        api_result: Dict[str, Any]
    ):
        """原子性处理绑卡成功"""
        # 1. 更新记录状态
        record.status = CardStatus.SUCCESS
        record.walmart_ck_id = ck.id
        record.department_id = ck.department_id
        record.response_data = api_result
        record.error_message = None
        record.updated_at = get_current_time()

        # 2. 确认CK预占用（不重复增加计数）- 修复重复计数问题
        # 注意：bind_count已在CK选择时预占用，这里只需要确认和更新时间
        ck.last_bind_time = get_current_time().isoformat()

        logger.info(
            f"绑卡成功，确认CK预占用: ck_id={ck.id}, "
            f"bind_count={ck.bind_count}/{ck.total_limit}"
        )

        # 3. 检查CK是否达到限制（确保状态一致性）
        if ck.total_limit and ck.bind_count >= ck.total_limit:
            if ck.active:
                ck.active = False
                logger.info(f"CK {ck.id} 达到限制({ck.total_limit})，确认禁用")
            else:
                logger.debug(f"CK {ck.id} 已处于禁用状态")

        # 4. 【关键修复】强制获取卡片余额信息
        # 从API结果中获取debug标识
        debug = api_result.get('debug', False)
        await self._fetch_and_save_balance(record, ck.id, debug)

        logger.info(f"绑卡成功处理完成: record={record.id}, ck={ck.id}, bind_count={ck.bind_count}")
    
    async def _handle_failure_atomic(
        self,
        record: CardRecord,
        api_result: Dict[str, Any]
    ):
        """原子性处理绑卡失败"""
        # 1. 更新记录状态
        record.status = CardStatus.FAILED
        record.response_data = api_result
        record.error_message = api_result.get('error', '绑卡失败')
        record.updated_at = get_current_time()

        # 2. 增加重试次数
        record.retry_count += 1

        # 3. 【关键修复】回滚CK预占用
        walmart_ck_id = api_result.get('walmart_ck_id')
        if walmart_ck_id:
            try:
                # 使用行锁获取CK，确保并发安全
                if self.is_async:
                    from sqlalchemy import select
                    ck_stmt = select(WalmartCK).filter(
                        WalmartCK.id == walmart_ck_id
                    ).with_for_update()
                    ck_result = await self.db.execute(ck_stmt)
                    ck = ck_result.scalar_one_or_none()
                else:
                    ck = self.db.query(WalmartCK).filter(
                        WalmartCK.id == walmart_ck_id
                    ).with_for_update().first()

                if ck and ck.bind_count > 0:
                    original_count = ck.bind_count
                    ck.bind_count -= 1

                    logger.info(
                        f"绑卡失败，回滚CK预占用: ck_id={walmart_ck_id}, "
                        f"bind_count={original_count} -> {ck.bind_count}"
                    )
                else:
                    logger.warning(f"CK {walmart_ck_id} 不存在或bind_count已为0，无法回滚")

            except Exception as e:
                logger.error(f"回滚CK预占用失败: {e} | walmart_ck_id={walmart_ck_id}")
                # 不抛出异常，避免影响记录状态更新
        else:
            logger.warning(f"API结果中缺少walmart_ck_id，无法回滚CK预占用 | record_id={record.id}")

        logger.info(f"绑卡失败处理完成: record={record.id}, retry_count={record.retry_count}")

    async def validate_binding_consistency(self, record_id: str) -> Dict[str, Any]:
        """验证绑卡数据一致性"""
        try:
            if self.is_async:
                from sqlalchemy import select
                stmt = select(CardRecord).filter(CardRecord.id == record_id)
                result = await self.db.execute(stmt)
                record = result.scalar_one_or_none()
            else:
                record = self.db.query(CardRecord).filter(CardRecord.id == record_id).first()

            if not record:
                return {'valid': False, 'error': '记录不存在'}
            
            if record.status != CardStatus.SUCCESS:
                return {'valid': True, 'message': '非成功记录，无需验证CK一致性'}
            
            if not record.walmart_ck_id:
                return {'valid': False, 'error': '成功记录缺少CK信息'}
            
            # 验证CK是否存在
            if self.is_async:
                ck_stmt = select(WalmartCK).filter(WalmartCK.id == record.walmart_ck_id)
                ck_result = await self.db.execute(ck_stmt)
                ck = ck_result.scalar_one_or_none()
            else:
                ck = self.db.query(WalmartCK).filter(WalmartCK.id == record.walmart_ck_id).first()

            if not ck:
                return {'valid': False, 'error': f'CK {record.walmart_ck_id} 不存在'}
            
            # 验证商户匹配
            if ck.merchant_id != record.merchant_id:
                return {'valid': False, 'error': f'CK商户不匹配: CK={ck.merchant_id}, Record={record.merchant_id}'}
            
            return {'valid': True, 'message': '数据一致性验证通过'}
            
        except Exception as e:
            logger.error(f"验证绑卡一致性失败: {e}")
            return {'valid': False, 'error': str(e)}

    async def _fetch_and_save_balance(self, record: CardRecord, walmart_ck_id: int, debug: bool = False):
        """
        获取并保存卡片余额信息

        Args:
            record: 卡记录对象
            walmart_ck_id: 沃尔玛CK ID
            debug: 测试模式标识，为True时返回模拟数据
        """
        try:
            # 【修复】使用支持异步会话的余额服务
            from app.services.async_balance_service import AsyncBalanceService

            balance_service = AsyncBalanceService(self.db)

            # 获取余额（带重试机制，支持异步和同步会话）
            success, balance_result = await balance_service.get_card_balance_with_retry(
                record=record,
                walmart_ck_id=walmart_ck_id,
                trace_id=getattr(record, 'trace_id', None),
                debug=debug
            )

            if success:
                # 保存余额信息到记录
                record.balance = balance_result.get("balance")
                record.cardBalance = balance_result.get("cardBalance")
                record.balanceCnt = balance_result.get("balanceCnt")

                logger.info(
                    f"[ATOMIC_BALANCE_SUCCESS] 余额获取成功并保存 | "
                    f"record_id={record.id} | balance={record.balance} | "
                    f"cardBalance={record.cardBalance} | balanceCnt={record.balanceCnt} | "
                    f"retry_count={balance_result.get('retry_count', 0)}"
                )
            else:
                # 余额获取失败，记录错误但不影响绑卡成功状态
                error_msg = balance_result.get("error_message", "未知错误")
                logger.error(
                    f"[ATOMIC_BALANCE_FAILED] 余额获取失败 | "
                    f"record_id={record.id} | error={error_msg} | "
                    f"retry_count={balance_result.get('retry_count', 0)}"
                )

                # 在记录中标记余额获取失败，但不影响绑卡状态
                # 可以考虑添加一个字段来记录余额获取状态
                if hasattr(record, 'balance_fetch_error'):
                    record.balance_fetch_error = error_msg

        except Exception as e:
            logger.error(
                f"[ATOMIC_BALANCE_EXCEPTION] 余额获取过程异常 | "
                f"record_id={record.id} | error={str(e)}"
            )
            # 余额获取异常不应该影响绑卡成功状态


class SafeBindingWrapper:
    """安全绑卡包装器 - 为现有绑卡流程提供原子性保护"""
    
    def __init__(self, db: Session):
        self.db = db
        self.atomic_service = AtomicBindingService(db)
    
    async def safe_update_bind_result(
        self,
        record_id: str,
        merchant_id: int,
        walmart_ck_id: int,
        bind_success: bool,
        api_result: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        安全的绑卡结果更新
        替代原有的_update_bind_result方法，确保原子性
        
        Args:
            record_id: 记录ID
            merchant_id: 商户ID
            walmart_ck_id: CK ID
            bind_success: 绑卡是否成功
            api_result: API结果
            
        Returns:
            bool: 更新是否成功
        """
        if not api_result:
            api_result = {'walmart_ck_id': walmart_ck_id}
        else:
            api_result['walmart_ck_id'] = walmart_ck_id
        
        return await self.atomic_service.execute_atomic_binding(
            record_id=record_id,
            merchant_id=merchant_id,
            api_result=api_result,
            is_success=bind_success
        )
    
    async def safe_record_ck_usage(self, ck_id: int, success: bool) -> bool:
        """
        安全的CK使用记录（已集成到原子性操作中）
        这个方法现在只是为了兼容性，实际的CK使用记录在原子性操作中完成
        """
        logger.info(f"CK使用记录已在原子性操作中完成: ck_id={ck_id}, success={success}")
        return True
    
    async def validate_and_fix_inconsistencies(self) -> Dict[str, Any]:
        """验证并修复数据不一致问题"""
        try:
            # 查找状态为success但没有CK信息的记录
            if self.is_async:
                from sqlalchemy import select
                stmt = select(CardRecord).filter(
                    CardRecord.status == CardStatus.SUCCESS,
                    CardRecord.walmart_ck_id.is_(None)
                )
                result = await self.db.execute(stmt)
                inconsistent_records = result.scalars().all()
            else:
                inconsistent_records = self.db.query(CardRecord).filter(
                    CardRecord.status == CardStatus.SUCCESS,
                    CardRecord.walmart_ck_id.is_(None)
                ).all()
            
            fixed_count = 0
            errors = []
            
            for record in inconsistent_records:
                try:
                    # 尝试从response_data中恢复CK信息
                    if record.response_data and 'walmart_ck_id' in record.response_data:
                        walmart_ck_id = record.response_data['walmart_ck_id']
                        
                        # 验证CK是否存在
                        if self.is_async:
                            ck_stmt = select(WalmartCK).filter(
                                WalmartCK.id == walmart_ck_id,
                                WalmartCK.merchant_id == record.merchant_id
                            )
                            ck_result = await self.db.execute(ck_stmt)
                            ck = ck_result.scalar_one_or_none()
                        else:
                            ck = self.db.query(WalmartCK).filter(
                                WalmartCK.id == walmart_ck_id,
                                WalmartCK.merchant_id == record.merchant_id
                            ).first()
                        
                        if ck:
                            record.walmart_ck_id = walmart_ck_id
                            record.department_id = ck.department_id
                            fixed_count += 1
                            logger.info(f"修复记录 {record.id} 的CK信息: {walmart_ck_id}")
                        else:
                            errors.append(f"记录 {record.id} 引用的CK {walmart_ck_id} 不存在")
                    else:
                        errors.append(f"记录 {record.id} 无法恢复CK信息")
                        
                except Exception as e:
                    errors.append(f"修复记录 {record.id} 失败: {e}")
            
            if fixed_count > 0:
                self.db.commit()
            
            return {
                'total_inconsistent': len(inconsistent_records),
                'fixed_count': fixed_count,
                'errors': errors
            }
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"验证和修复不一致问题失败: {e}")
            return {'error': str(e)}


# 工厂函数
def create_atomic_binding_service(db: Session) -> AtomicBindingService:
    """创建原子性绑卡服务"""
    return AtomicBindingService(db)


def create_safe_binding_wrapper(db: Session) -> SafeBindingWrapper:
    """创建安全绑卡包装器"""
    return SafeBindingWrapper(db)
