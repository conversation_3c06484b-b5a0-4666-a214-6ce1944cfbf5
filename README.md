# 沃尔玛绑卡系统并发测试工具

🚀 用于测试Python沃尔玛绑卡系统的绑卡API接口并发处理能力，验证事件循环阻塞问题是否已解决。

## 功能特性

- ✅ **真正的并发测试**：使用Go的goroutine实现真正的并发请求
- ✅ **唯一卡号生成**：自动生成唯一卡号，避免重复绑卡错误
- ✅ **正确的API签名**：严格按照沃尔玛API规范计算签名（排除debug参数）
- ✅ **详细的性能统计**：成功率、响应时间、QPS等关键指标
- ✅ **实时进度显示**：实时显示每个请求的执行状态
- ✅ **智能性能评估**：自动分析系统性能并给出改进建议
- ✅ **结果文件保存**：详细结果保存为JSON文件便于分析

## 快速开始

### 1. 编译程序

```bash
# 初始化Go模块
go mod tidy

# 编译程序
go build -o walmart-test main.go

# 或者直接运行
go run main.go --help
```

### 2. 基本使用

```bash
# 基本并发测试（10个并发，50个请求）
./walmart-test \
  --url "http://localhost:8000" \
  --key "your_api_key" \
  --secret "your_api_secret" \
  --merchant "your_merchant_code" \
  --concurrency 10 \
  --requests 50

# 持续负载测试（10个并发，持续30秒）
./walmart-test \
  --url "http://localhost:8000" \
  --key "your_api_key" \
  --secret "your_api_secret" \
  --merchant "your_merchant_code" \
  --concurrency 10 \
  --duration 30
```

### 3. 参数说明

| 参数 | 短参数 | 必需 | 默认值 | 说明 |
|------|--------|------|--------|------|
| `--url` | `-u` | 否 | `http://localhost:8000` | API基础URL |
| `--key` | `-k` | 是 | - | API密钥 |
| `--secret` | `-s` | 是 | - | API密文 |
| `--merchant` | `-m` | 是 | - | 商户代码 |
| `--concurrency` | `-c` | 否 | `10` | 并发数量 |
| `--requests` | `-r` | 否 | `50` | 总请求数（duration=0时使用） |
| `--duration` | `-d` | 否 | `0` | 测试持续时间（秒），0表示使用requests参数 |
| `--timeout` | `-t` | 否 | `30` | 请求超时时间（秒） |
| `--debug` | - | 否 | `true` | 启用调试模式 |

## 测试场景

### 1. 验证阻塞问题修复

测试之前导致系统崩溃的10个并发请求：

```bash
./walmart-test \
  --key "your_api_key" \
  --secret "your_api_secret" \
  --merchant "your_merchant_code" \
  --concurrency 10 \
  --requests 50
```

**预期结果**：
- 成功率 ≥ 95%
- 平均响应时间 ≤ 5秒
- 系统不会崩溃或无响应

### 2. 压力测试

测试系统的极限处理能力：

```bash
# 20个并发
./walmart-test \
  --key "your_api_key" \
  --secret "your_api_secret" \
  --merchant "your_merchant_code" \
  --concurrency 20 \
  --requests 100

# 30个并发
./walmart-test \
  --key "your_api_key" \
  --secret "your_api_secret" \
  --merchant "your_merchant_code" \
  --concurrency 30 \
  --requests 150
```

### 3. 稳定性测试

长时间持续负载测试：

```bash
# 持续5分钟的10个并发
./walmart-test \
  --key "your_api_key" \
  --secret "your_api_secret" \
  --merchant "your_merchant_code" \
  --concurrency 10 \
  --duration 300
```

## 结果分析

### 输出示例

```
🚀 沃尔玛绑卡系统并发测试工具
============================================================

📋 测试配置:
  API地址: http://localhost:8000
  商户代码: TEST_MERCHANT
  并发数: 10
  请求总数: 50
  请求超时: 30秒
  调试模式: true

▶ 开始固定请求数测试: 50个请求, 10并发

[15:30:01] 请求 #1 | 卡号: ***12345678 | 状态: OK | 耗时: 1.234s | HTTP: 200
[15:30:01] 请求 #2 | 卡号: ***87654321 | 状态: OK | 耗时: 1.456s | HTTP: 200
...

📊 测试结果报告
============================================================

📈 基本统计:
  总请求数: 50
  成功请求: 48
  失败请求: 2
  成功率: 96.00%
  并发数: 10

⚡ 性能统计:
  总耗时: 12.345s
  平均响应时间: 2.456s
  最快响应时间: 1.123s
  最慢响应时间: 5.678s
  QPS (每秒请求数): 4.05

🎯 性能评估:
  并发处理能力: ✅ (优秀)
    系统能够稳定处理 10 个并发请求
  响应时间: ⚡ (快)
  吞吐量: 📉 (低)
  系统稳定性: ✅ (稳定)

💡 建议:
  • ✅ 系统表现良好！可以尝试:
    - 逐步增加并发数测试极限
    - 进行长时间稳定性测试
    - 监控生产环境性能指标

💾 详细结果已保存到: test_results_20240125_153045.json
```

### 性能指标说明

| 指标 | 优秀 | 良好 | 需改进 |
|------|------|------|--------|
| 成功率 | ≥95% | ≥80% | <80% |
| 平均响应时间 | ≤1s | ≤3s | >5s |
| QPS | ≥50 | ≥20 | <20 |

## API签名算法

工具严格按照沃尔玛API规范实现签名算法：

1. **参数排序**：按参数名字典序排序
2. **排除debug参数**：debug参数不参与签名计算
3. **构建签名字符串**：`param1=value1&param2=value2&key=api_secret`
4. **MD5加密**：对签名字符串进行MD5加密
5. **转大写**：将MD5结果转为大写

### 签名示例

```go
// 原始参数
params := map[string]string{
    "merchant_code": "TEST_MERCHANT",
    "card_number": "2326123456789012",  // 必须以2326开头
    "amount": "100.00",
    "timestamp": "1640995200",
    "debug": "true",  // 此参数不参与签名
}

// 签名字符串（排除debug）
signStr := "amount=100.00&card_number=2326123456789012&merchant_code=TEST_MERCHANT&timestamp=1640995200&key=your_secret"

// MD5加密并转大写
signature := strings.ToUpper(md5(signStr))
```

## 卡号生成规则

工具自动生成符合沃尔玛系统要求的卡号：

### 卡号格式
- **前缀要求**：必须以 `2326` 开头
- **总长度**：20位数字
- **格式**：`2326` + `时间戳后4位` + `请求ID后4位` + `8位随机数`

### 生成示例
```go
// 示例卡号：23261234000100012345
// 分解：
// 2326     - 固定前缀（系统要求）
// 1234     - 时间戳后4位（确保时间唯一性）
// 0001     - 请求ID后4位（确保请求唯一性）
// 00012345 - 8位随机数（确保随机性）
```

### 验证规则
- ✅ 以 `2326` 开头（系统强制要求）
- ✅ 长度至少6位（系统最小要求）
- ✅ 全数字格式
- ✅ 每个请求生成唯一卡号

## 故障排除

### 常见问题

1. **连接被拒绝**
   ```
   错误: dial tcp 127.0.0.1:8000: connect: connection refused
   ```
   - 检查API服务是否启动
   - 确认端口号是否正确

2. **签名验证失败**
   ```
   HTTP: 401, 响应: {"error": "Invalid signature"}
   ```
   - 检查API密钥和密文是否正确
   - 确认商户代码是否正确

3. **请求超时**
   ```
   错误: context deadline exceeded
   ```
   - 增加超时时间：`--timeout 60`
   - 检查网络连接
   - 检查服务器性能

4. **高并发失败**
   ```
   成功率: 45.00%
   ```
   - 这可能表明系统仍有性能问题
   - 检查服务器资源使用情况
   - 查看应用程序日志

## 开发说明

### 项目结构

```
walmart-bind-card-concurrency-testing/
├── main.go                 # 主程序
├── go.mod                  # Go模块文件
├── config.example.json     # 配置示例
├── README.md              # 使用说明
└── test_results_*.json    # 测试结果文件（自动生成）
```

### 扩展功能

可以根据需要添加以下功能：

1. **配置文件支持**：从JSON文件读取测试配置
2. **多场景测试**：支持批量执行多个测试场景
3. **图表生成**：生成性能趋势图表
4. **告警功能**：性能指标异常时发送告警
5. **数据库存储**：将测试结果存储到数据库

## 许可证

MIT License
