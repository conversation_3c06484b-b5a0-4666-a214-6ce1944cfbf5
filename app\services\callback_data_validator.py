"""
回调数据验证和格式化服务
确保回调参数的准确性、完整性和格式正确性
"""

import logging
from typing import Dict, Any, Optional, Union
from decimal import Decimal, InvalidOperation
from datetime import datetime
import re

from app.models.card_record import CardRecord, CardStatus

logger = logging.getLogger(__name__)

# 回调参数配置
CALLBACK_REQUIRED_FIELDS = [
    "merchantOrderId", "cardNumber", "status", "balance"
]

CALLBACK_BALANCE_FIELDS = ["balance", "cardBalance", "balanceCnt"]
CALLBACK_AMOUNT_FIELDS = ["amount", "actual_amount"]
CALLBACK_TIME_FIELDS = ["requestTime", "processTime"]

# 数据类型映射
FIELD_TYPE_MAPPING = {
    "traceId": str,
    "merchantOrderId": str,
    "cardNumber": str,
    "status": str,
    "amount": (int, float, type(None)),
    "actual_amount": (int, float, type(None)),
    "balance": (int, float, type(None)),
    "cardBalance": (int, float, type(None)),
    "balanceCnt": (int, float, type(None)),
    "retryCount": int,
    "requestTime": (str, type(None)),
    "processTime": (str, type(None)),
    "failReason": (str, type(None)),
    "extData": (str, type(None))
}


class CallbackDataValidator:
    """回调数据验证和格式化服务"""
    
    def __init__(self):
        self.validation_errors = []
        self.validation_warnings = []
    
    def prepare_and_validate_callback_data(
        self,
        record: CardRecord,
        trace_id: Optional[str],
        retry_count: int,
        ext_data: Optional[str],
        balance_fetch_success: bool = True,
        suppress_logging: bool = False
    ) -> Dict[str, Any]:
        """
        准备并验证回调数据

        Args:
            record: 卡记录对象
            trace_id: 追踪ID
            retry_count: 重试次数
            ext_data: 扩展数据
            balance_fetch_success: 余额获取是否成功
            suppress_logging: 是否抑制日志输出（避免重复日志）

        Returns:
            Dict[str, Any]: 验证后的回调数据
        """
        self.validation_errors = []
        self.validation_warnings = []
        
        # 1. 准备基础回调数据
        callback_data = self._prepare_base_callback_data(
            record, trace_id, retry_count, ext_data
        )
        
        # 2. 处理余额相关字段
        self._process_balance_fields(callback_data, record, balance_fetch_success)
        
        # 3. 处理金额字段
        self._process_amount_fields(callback_data, record)
        
        # 4. 处理时间字段
        self._process_time_fields(callback_data, record)
        
        # 5. 处理状态和错误信息
        self._process_status_and_error_fields(callback_data, record)
        
        
        # 8. 处理卡号（保持完整，不脱敏）
        self._process_card_number(callback_data, record)
        
        # 9. 记录验证结果（如果未抑制日志）
        if not suppress_logging:
            self._log_validation_results(record.id, callback_data)
        
        return callback_data
    
    def _prepare_base_callback_data(
        self, 
        record: CardRecord, 
        trace_id: Optional[str], 
        retry_count: int, 
        ext_data: Optional[str]
    ) -> Dict[str, Any]:
        """准备基础回调数据"""
        return {
            "traceId": trace_id or record.trace_id or "",
            "merchantOrderId": record.merchant_order_id or "",
            "cardNumber": record.card_number or "",
            "status": self._normalize_status(record.status),
            "retryCount": retry_count,
            "extData": ext_data,
            # 初始化其他字段为None，后续处理
            "amount": None,
            "actual_amount": None,
            "balance": None,
            "cardBalance": None,
            "balanceCnt": None,
            "requestTime": None,
            "processTime": None,
            "failReason": None
        }
    
    def _process_balance_fields(
        self, 
        callback_data: Dict[str, Any], 
        record: CardRecord, 
        balance_fetch_success: bool
    ):
        """处理余额相关字段"""
        if balance_fetch_success:
            # 余额获取成功，填充实际余额数据
            callback_data["balance"] = self._format_decimal_value(record.balance, "balance")
            callback_data["cardBalance"] = self._format_decimal_value(record.cardBalance, "cardBalance")
            callback_data["balanceCnt"] = self._format_decimal_value(record.balanceCnt, "balanceCnt")
            
            # 验证余额数据的一致性
            self._validate_balance_consistency(callback_data, record.id)
        else:
            # 余额获取失败，设置为None并记录警告
            callback_data["balance"] = None
            callback_data["cardBalance"] = None
            callback_data["balanceCnt"] = None
            
            self.validation_warnings.append({
                "field": "balance_fields",
                "message": "余额获取失败，余额字段设置为null",
                "impact": "商户需要根据status判断绑卡结果"
            })
    
    def _process_amount_fields(self, callback_data: Dict[str, Any], record: CardRecord):
        """处理金额字段"""
        # 处理请求金额（分）
        callback_data["amount"] = self._format_amount_value(record.amount, "amount")
        
        # 处理实际金额（分）
        callback_data["actual_amount"] = self._format_amount_value(record.actual_amount, "actual_amount")
    
    def _process_time_fields(self, callback_data: Dict[str, Any], record: CardRecord):
        """处理时间字段"""
        # 请求时间
        callback_data["requestTime"] = self._format_datetime(record.created_at, "requestTime")
        
        # 处理时间（只有在绑卡完成时才设置）
        if record.status in [CardStatus.SUCCESS, CardStatus.FAILED]:
            callback_data["processTime"] = self._format_datetime(record.updated_at, "processTime")
        else:
            callback_data["processTime"] = None
    
    def _process_status_and_error_fields(self, callback_data: Dict[str, Any], record: CardRecord):
        """处理状态和错误信息字段"""
        # 标准化状态值
        callback_data["status"] = self._normalize_status(record.status)
        
        # 处理错误信息
        if record.status == CardStatus.FAILED and record.error_message:
            callback_data["failReason"] = str(record.error_message)[:500]  # 限制长度
        else:
            callback_data["failReason"] = None
    
    def _process_card_number(self, callback_data: Dict[str, Any], record: CardRecord):
        """处理卡号（保持完整，不脱敏）"""
        card_number = record.card_number
        if card_number:
            # 回调中保持卡号完整，不进行脱敏
            callback_data["cardNumber"] = str(card_number).strip()

            # 验证卡号格式（基本长度检查）
            if len(card_number) < 8:
                self.validation_warnings.append({
                    "field": "cardNumber",
                    "message": "卡号长度异常，可能影响商户处理",
                    "length": len(card_number)
                })
        else:
            # 卡号为空的情况
            callback_data["cardNumber"] = ""
            self.validation_errors.append({
                "field": "cardNumber",
                "error": "卡号为空，商户无法处理",
                "value": card_number
            })
    
    
    def _validate_balance_consistency(self, callback_data: Dict[str, Any], record_id: int):
        """验证余额数据的一致性"""
        balance = callback_data.get("balance")
        card_balance = callback_data.get("cardBalance")
        balance_cnt = callback_data.get("balanceCnt")
        
        # 检查是否至少有一个余额字段有值
        has_balance_data = any(v is not None for v in [balance, card_balance, balance_cnt])
        
        if not has_balance_data:
            self.validation_warnings.append({
                "field": "balance_consistency",
                "message": "所有余额字段都为空",
                "record_id": record_id
            })
    
    def _format_decimal_value(self, value: Any, field_name: str) -> Optional[Union[int, float]]:
        """格式化小数值"""
        if value is None:
            return None
        
        try:
            if isinstance(value, (int, float)):
                return float(value) if value != int(value) else int(value)
            elif isinstance(value, str):
                decimal_value = Decimal(value)
                float_value = float(decimal_value)
                return float_value if float_value != int(float_value) else int(float_value)
            else:
                self.validation_warnings.append({
                    "field": field_name,
                    "message": f"无法识别的数值类型: {type(value)}",
                    "value": value
                })
                return None
        except (InvalidOperation, ValueError) as e:
            self.validation_errors.append({
                "field": field_name,
                "error": f"数值格式化失败: {str(e)}",
                "value": value
            })
            return None
    
    def _format_amount_value(self, value: Any, field_name: str) -> Optional[int]:
        """格式化金额值（分）"""
        if value is None:
            return None
        
        try:
            if isinstance(value, (int, float)):
                return int(value)
            elif isinstance(value, str):
                return int(float(value))
            else:
                self.validation_warnings.append({
                    "field": field_name,
                    "message": f"无法识别的金额类型: {type(value)}",
                    "value": value
                })
                return None
        except (ValueError, TypeError) as e:
            self.validation_errors.append({
                "field": field_name,
                "error": f"金额格式化失败: {str(e)}",
                "value": value
            })
            return None
    
    def _format_datetime(self, dt: Optional[datetime], field_name: str) -> Optional[str]:
        """格式化日期时间为ISO格式"""
        if dt is None:
            return None
        
        try:
            # 确保时间格式符合ISO 8601标准
            iso_string = dt.isoformat()
            # 验证格式是否正确
            if not re.match(r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}', iso_string):
                raise ValueError(f"时间格式不符合ISO标准: {iso_string}")
            return iso_string
        except Exception as e:
            self.validation_errors.append({
                "field": field_name,
                "error": f"时间格式化失败: {str(e)}",
                "value": dt
            })
            return None
    
    def _normalize_status(self, status: str) -> str:
        """标准化状态值"""
        if not status:
            return "unknown"
        
        status_lower = status.lower()
        if status_lower in ["success", "成功"]:
            return "success"
        elif status_lower in ["failed", "failure", "失败"]:
            return "failed"
        elif status_lower in ["pending", "processing", "待处理", "处理中"]:
            return "processing"
        else:
            self.validation_warnings.append({
                "field": "status",
                "message": f"未知的状态值: {status}",
                "normalized_to": status_lower
            })
            return status_lower
    
    def _log_validation_results(self, record_id: int, callback_data: Dict[str, Any]):
        """记录验证结果"""
        if self.validation_errors:
            logger.error(
                f"[CALLBACK_VALIDATION_ERROR] 回调数据验证失败 | "
                f"record_id={record_id} | errors={len(self.validation_errors)} | "
                f"details={self.validation_errors}"
            )
        
        if self.validation_warnings:
            logger.warning(
                f"[CALLBACK_VALIDATION_WARNING] 回调数据验证警告 | "
                f"record_id={record_id} | warnings={len(self.validation_warnings)} | "
                f"details={self.validation_warnings}"
            )
        
        if not self.validation_errors and not self.validation_warnings:
            logger.info(
                f"[CALLBACK_VALIDATION_SUCCESS] 回调数据验证通过 | "
                f"record_id={record_id} | fields={list(callback_data.keys())}"
            )
    
    def get_validation_summary(self) -> Dict[str, Any]:
        """获取验证结果摘要"""
        return {
            "has_errors": len(self.validation_errors) > 0,
            "has_warnings": len(self.validation_warnings) > 0,
            "error_count": len(self.validation_errors),
            "warning_count": len(self.validation_warnings),
            "errors": self.validation_errors,
            "warnings": self.validation_warnings
        }
