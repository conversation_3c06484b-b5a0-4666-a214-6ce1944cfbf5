# Walmart Card Binding Server - Linux 系统服务

这个目录包含了将 Walmart Card Binding Server 配置为 Linux 系统服务的所有必要文件。

## 📁 文件说明

- `walmart-bind-card.service` - systemd 服务配置文件
- `install-service.sh` - 服务安装脚本
- `uninstall-service.sh` - 服务卸载脚本
- `health-check.sh` - 健康检查脚本
- `README-Service.md` - 本说明文档

## 🚀 快速安装

### 1. 准备工作

确保您的系统满足以下要求：
- Linux 系统（支持 systemd）
- root 权限
- 已编译好的执行文件或 Python 环境

### 2. 安装服务

```bash
# 给安装脚本执行权限
chmod +x install-service.sh

# 运行安装脚本（需要 root 权限）
sudo ./install-service.sh
```

### 3. 配置执行文件路径

安装完成后，您需要：

1. 将您的执行文件复制到 `/opt/walmart-bind-card-server/`
2. 如果是 Python 脚本，修改服务文件中的 `ExecStart` 路径

```bash
# 编辑服务文件
sudo nano /etc/systemd/system/walmart-bind-card.service

# 修改 ExecStart 行，例如：
# ExecStart=/opt/walmart-bind-card-server/your-executable
# 或者对于 Python 脚本：
# ExecStart=/usr/bin/python3 /opt/walmart-bind-card-server/app/main.py
```

### 4. 重新加载并启动服务

```bash
# 重新加载 systemd 配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start walmart-bind-card

# 检查服务状态
sudo systemctl status walmart-bind-card
```

## 🛠️ 服务管理

### 使用便捷管理脚本

安装完成后，您可以使用 `walmart-service` 命令：

```bash
# 启动服务
walmart-service start

# 停止服务
walmart-service stop

# 重启服务
walmart-service restart

# 查看状态
walmart-service status

# 查看实时日志
walmart-service logs

# 启用开机自启
walmart-service enable

# 禁用开机自启
walmart-service disable
```

### 使用 systemctl 命令

```bash
# 启动服务
sudo systemctl start walmart-bind-card

# 停止服务
sudo systemctl stop walmart-bind-card

# 重启服务
sudo systemctl restart walmart-bind-card

# 查看状态
sudo systemctl status walmart-bind-card

# 启用开机自启
sudo systemctl enable walmart-bind-card

# 禁用开机自启
sudo systemctl disable walmart-bind-card

# 查看日志
sudo journalctl -u walmart-bind-card -f
```

## 📊 健康检查

### 运行健康检查

```bash
# 给健康检查脚本执行权限
chmod +x health-check.sh

# 运行完整健康检查
./health-check.sh

# 静默模式（只显示错误）
./health-check.sh -q

# 详细模式
./health-check.sh -v
```

### 设置定时健康检查

您可以将健康检查添加到 crontab 中：

```bash
# 编辑 crontab
sudo crontab -e

# 添加以下行（每5分钟检查一次）
*/5 * * * * /path/to/health-check.sh -q >> /var/log/walmart-bind-card/health-check.log 2>&1
```

## 🗂️ 目录结构

安装后的目录结构：

```
/opt/walmart-bind-card-server/
├── your-executable          # 您的执行文件
├── logs/                    # 应用日志目录
├── data/                    # 数据目录
└── config/                  # 配置文件目录（可选）

/var/log/walmart-bind-card/  # 系统日志目录

/etc/systemd/system/
└── walmart-bind-card.service # systemd 服务文件

/usr/local/bin/
└── walmart-service          # 管理脚本

/etc/logrotate.d/
└── walmart-bind-card        # 日志轮转配置
```

## 🔧 配置说明

### 服务配置

主要配置项在 `walmart-bind-card.service` 文件中：

- `ExecStart`: 执行文件路径
- `User/Group`: 运行用户和组
- `WorkingDirectory`: 工作目录
- `Environment`: 环境变量
- `Restart`: 重启策略

### 自动重启配置

服务配置了以下自动重启策略：

- `Restart=always`: 总是重启
- `RestartSec=10`: 重启间隔10秒
- `StartLimitInterval=60`: 60秒内
- `StartLimitBurst=3`: 最多重启3次

### 资源限制

- `LimitNOFILE=65536`: 文件描述符限制
- `LimitNPROC=4096`: 进程数限制

## 🔍 故障排除

### 查看服务状态

```bash
# 查看详细状态
sudo systemctl status walmart-bind-card -l

# 查看最近的日志
sudo journalctl -u walmart-bind-card --since "1 hour ago"

# 查看实时日志
sudo journalctl -u walmart-bind-card -f
```

### 常见问题

1. **服务启动失败**
   - 检查执行文件路径是否正确
   - 检查文件权限
   - 查看日志获取详细错误信息

2. **权限问题**
   - 确保服务用户有执行权限
   - 检查目录权限设置

3. **依赖服务未启动**
   - 确保 MySQL 和 RabbitMQ 服务正在运行
   - 检查网络连接

## 🗑️ 卸载服务

如果需要卸载服务：

```bash
# 给卸载脚本执行权限
chmod +x uninstall-service.sh

# 运行卸载脚本
sudo ./uninstall-service.sh
```

卸载脚本会：
1. 停止并禁用服务
2. 删除 systemd 服务文件
3. 删除管理脚本
4. 删除日志轮转配置
5. 可选择删除用户、目录和日志文件

## 📝 注意事项

1. **安全性**: 服务运行在专用用户账户下，具有最小权限
2. **日志管理**: 配置了日志轮转，防止日志文件过大
3. **监控**: 建议配置监控系统监控服务状态
4. **备份**: 定期备份配置文件和数据目录

## 🆘 支持

如果遇到问题，请：
1. 查看服务日志
2. 运行健康检查脚本
3. 检查系统资源使用情况
4. 联系技术支持团队
