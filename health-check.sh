#!/bin/bash

# Walmart Card Binding Server - Health Check Script
# 沃尔玛绑卡服务健康检查脚本

# 配置变量
SERVICE_NAME="walmart-bind-card"
API_URL="http://localhost:20000"
HEALTH_ENDPOINT="/health"
TIMEOUT=10
MAX_RETRIES=3

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务状态
check_service_status() {
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_info "✅ 服务运行状态: 正常"
        return 0
    else
        log_error "❌ 服务运行状态: 异常"
        return 1
    fi
}

# 检查端口监听
check_port_listening() {
    local port=20000
    
    if netstat -tuln 2>/dev/null | grep -q ":$port "; then
        log_info "✅ 端口监听状态: 端口 $port 正常监听"
        return 0
    elif ss -tuln 2>/dev/null | grep -q ":$port "; then
        log_info "✅ 端口监听状态: 端口 $port 正常监听"
        return 0
    else
        log_error "❌ 端口监听状态: 端口 $port 未监听"
        return 1
    fi
}

# 检查HTTP健康端点
check_http_health() {
    local url="${API_URL}${HEALTH_ENDPOINT}"
    local retry=0
    
    while [ $retry -lt $MAX_RETRIES ]; do
        if command -v curl >/dev/null 2>&1; then
            # 使用curl检查
            local response=$(curl -s -w "%{http_code}" -o /dev/null --connect-timeout $TIMEOUT "$url" 2>/dev/null)
            if [ "$response" = "200" ]; then
                log_info "✅ HTTP健康检查: 正常 (HTTP $response)"
                return 0
            fi
        elif command -v wget >/dev/null 2>&1; then
            # 使用wget检查
            if wget -q --timeout=$TIMEOUT --tries=1 -O /dev/null "$url" 2>/dev/null; then
                log_info "✅ HTTP健康检查: 正常"
                return 0
            fi
        fi
        
        retry=$((retry + 1))
        if [ $retry -lt $MAX_RETRIES ]; then
            log_warn "⚠️  HTTP健康检查失败，重试 $retry/$MAX_RETRIES..."
            sleep 2
        fi
    done
    
    log_error "❌ HTTP健康检查: 失败 (重试 $MAX_RETRIES 次后仍然失败)"
    return 1
}

# 检查数据库连接
check_database_connection() {
    # 这里可以添加数据库连接检查
    # 例如：mysql -h localhost -u user -p password -e "SELECT 1" >/dev/null 2>&1
    log_info "ℹ️  数据库连接检查: 跳过 (需要配置数据库连接参数)"
    return 0
}

# 检查RabbitMQ连接
check_rabbitmq_connection() {
    # 这里可以添加RabbitMQ连接检查
    # 例如：rabbitmqctl status >/dev/null 2>&1
    log_info "ℹ️  RabbitMQ连接检查: 跳过 (需要配置RabbitMQ连接参数)"
    return 0
}

# 检查磁盘空间
check_disk_space() {
    local install_dir="/opt/walmart-bind-card-server"
    local log_dir="/var/log/walmart-bind-card"
    local threshold=90
    
    # 检查安装目录磁盘空间
    if [ -d "$install_dir" ]; then
        local usage=$(df "$install_dir" | awk 'NR==2 {print $5}' | sed 's/%//')
        if [ "$usage" -gt "$threshold" ]; then
            log_warn "⚠️  磁盘空间警告: 安装目录磁盘使用率 ${usage}% (阈值: ${threshold}%)"
        else
            log_info "✅ 磁盘空间检查: 安装目录正常 (${usage}%)"
        fi
    fi
    
    # 检查日志目录磁盘空间
    if [ -d "$log_dir" ]; then
        local usage=$(df "$log_dir" | awk 'NR==2 {print $5}' | sed 's/%//')
        if [ "$usage" -gt "$threshold" ]; then
            log_warn "⚠️  磁盘空间警告: 日志目录磁盘使用率 ${usage}% (阈值: ${threshold}%)"
        else
            log_info "✅ 磁盘空间检查: 日志目录正常 (${usage}%)"
        fi
    fi
}

# 检查内存使用
check_memory_usage() {
    local service_pid=$(systemctl show --property MainPID --value "$SERVICE_NAME")
    
    if [ "$service_pid" != "0" ] && [ -n "$service_pid" ]; then
        if [ -f "/proc/$service_pid/status" ]; then
            local memory_kb=$(grep VmRSS /proc/$service_pid/status | awk '{print $2}')
            local memory_mb=$((memory_kb / 1024))
            log_info "✅ 内存使用检查: 服务使用 ${memory_mb}MB 内存"
        else
            log_warn "⚠️  内存使用检查: 无法获取进程内存信息"
        fi
    else
        log_warn "⚠️  内存使用检查: 无法获取服务进程ID"
    fi
}

# 检查日志错误
check_recent_errors() {
    local error_count=$(journalctl -u "$SERVICE_NAME" --since "1 hour ago" --no-pager | grep -i "error\|exception\|failed" | wc -l)
    
    if [ "$error_count" -eq 0 ]; then
        log_info "✅ 错误日志检查: 最近1小时无错误"
    elif [ "$error_count" -lt 10 ]; then
        log_warn "⚠️  错误日志检查: 最近1小时有 $error_count 个错误"
    else
        log_error "❌ 错误日志检查: 最近1小时有 $error_count 个错误 (过多)"
    fi
}

# 主健康检查函数
main_health_check() {
    echo "=========================================="
    echo "  Walmart Card Binding Server 健康检查"
    echo "=========================================="
    echo "检查时间: $(date)"
    echo ""
    
    local overall_status=0
    
    # 执行各项检查
    check_service_status || overall_status=1
    check_port_listening || overall_status=1
    check_http_health || overall_status=1
    check_database_connection
    check_rabbitmq_connection
    check_disk_space
    check_memory_usage
    check_recent_errors
    
    echo ""
    echo "=========================================="
    
    if [ $overall_status -eq 0 ]; then
        log_info "🎉 整体健康状态: 良好"
        echo "=========================================="
        exit 0
    else
        log_error "⚠️  整体健康状态: 存在问题"
        echo "=========================================="
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -q, --quiet    静默模式，只输出错误"
    echo "  -v, --verbose  详细模式，显示更多信息"
    echo ""
    echo "示例:"
    echo "  $0              # 执行完整健康检查"
    echo "  $0 -q           # 静默模式检查"
    echo "  $0 -v           # 详细模式检查"
}

# 处理命令行参数
case "$1" in
    -h|--help)
        show_help
        exit 0
        ;;
    -q|--quiet)
        # 静默模式：重定向输出，只显示错误
        main_health_check 2>&1 | grep -E "(ERROR|❌)"
        exit ${PIPESTATUS[0]}
        ;;
    -v|--verbose)
        # 详细模式：显示所有信息
        main_health_check
        ;;
    "")
        # 默认模式
        main_health_check
        ;;
    *)
        echo "未知选项: $1"
        show_help
        exit 1
        ;;
esac
