package utils

import (
	"context"
	"fmt"
	"runtime"
	"runtime/debug"
	"sync"
	"sync/atomic"
	"time"

	"go.uber.org/zap"
)

// Task 任务接口
type Task interface {
	Execute(ctx context.Context) error
	GetID() string
	GetPriority() int
}

// TaskFunc 函数式任务
type TaskFunc struct {
	ID       string
	Priority int
	Fn       func(ctx context.Context) error
}

func (t *TaskFunc) Execute(ctx context.Context) error {
	return t.Fn(ctx)
}

func (t *TaskFunc) GetID() string {
	return t.ID
}

func (t *TaskFunc) GetPriority() int {
	return t.Priority
}

// GoroutinePoolConfig 协程池配置
type GoroutinePoolConfig struct {
	CoreSize     int           // 核心协程数
	MaxSize      int           // 最大协程数
	KeepAlive    time.Duration // 空闲协程保活时间
	QueueSize    int           // 任务队列大小
	RejectPolicy string        // 拒绝策略
}

// GoroutinePool 协程池
type GoroutinePool struct {
	config       GoroutinePoolConfig
	taskQueue    chan Task
	workerCount  int64
	activeCount  int64
	totalTasks   int64
	completedTasks int64
	rejectedTasks  int64
	
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
	logger *zap.Logger
	
	// 监控指标
	metrics struct {
		sync.RWMutex
		avgExecutionTime time.Duration
		maxExecutionTime time.Duration
		minExecutionTime time.Duration
		lastUpdateTime   time.Time
	}
}

// NewGoroutinePool 创建新的协程池
func NewGoroutinePool(config GoroutinePoolConfig, logger *zap.Logger) *GoroutinePool {
	ctx, cancel := context.WithCancel(context.Background())
	
	pool := &GoroutinePool{
		config:    config,
		taskQueue: make(chan Task, config.QueueSize),
		ctx:       ctx,
		cancel:    cancel,
		logger:    logger,
	}
	
	// 启动核心协程
	for i := 0; i < config.CoreSize; i++ {
		pool.startWorker(true)
	}
	
	// 启动监控协程
	go pool.monitor()
	
	return pool
}

// Submit 提交任务
func (p *GoroutinePool) Submit(task Task) error {
	select {
	case p.taskQueue <- task:
		atomic.AddInt64(&p.totalTasks, 1)
		
		// 检查是否需要启动新的工作协程
		if p.shouldStartNewWorker() {
			p.startWorker(false)
		}
		
		return nil
	default:
		// 队列满了，根据拒绝策略处理
		return p.handleRejection(task)
	}
}

// SubmitFunc 提交函数任务
func (p *GoroutinePool) SubmitFunc(id string, priority int, fn func(ctx context.Context) error) error {
	task := &TaskFunc{
		ID:       id,
		Priority: priority,
		Fn:       fn,
	}
	return p.Submit(task)
}

// shouldStartNewWorker 判断是否应该启动新的工作协程
func (p *GoroutinePool) shouldStartNewWorker() bool {
	currentWorkers := atomic.LoadInt64(&p.workerCount)
	activeWorkers := atomic.LoadInt64(&p.activeCount)
	
	// 如果活跃协程数接近总协程数，且总协程数小于最大值，则启动新协程
	return activeWorkers >= currentWorkers*8/10 && currentWorkers < int64(p.config.MaxSize)
}

// startWorker 启动工作协程
func (p *GoroutinePool) startWorker(isCore bool) {
	atomic.AddInt64(&p.workerCount, 1)
	p.wg.Add(1)
	
	go func() {
		defer func() {
			atomic.AddInt64(&p.workerCount, -1)
			p.wg.Done()
		}()
		
		keepAliveTimer := time.NewTimer(p.config.KeepAlive)
		defer keepAliveTimer.Stop()
		
		for {
			select {
			case task := <-p.taskQueue:
				p.executeTask(task)
				
				// 重置保活定时器
				if !isCore {
					keepAliveTimer.Reset(p.config.KeepAlive)
				}
				
			case <-keepAliveTimer.C:
				// 非核心协程超时退出
				if !isCore {
					return
				}
				keepAliveTimer.Reset(p.config.KeepAlive)
				
			case <-p.ctx.Done():
				return
			}
		}
	}()
}

// executeTask 执行任务
func (p *GoroutinePool) executeTask(task Task) {
	atomic.AddInt64(&p.activeCount, 1)
	defer atomic.AddInt64(&p.activeCount, -1)
	
	startTime := time.Now()
	
	defer func() {
		if r := recover(); r != nil {
			p.logger.Error("任务执行panic",
				zap.String("task_id", task.GetID()),
				zap.Any("panic", r),
				zap.String("stack", string(debug.Stack())))
		}
		
		// 更新执行时间统计
		executionTime := time.Since(startTime)
		p.updateMetrics(executionTime)
		atomic.AddInt64(&p.completedTasks, 1)
	}()
	
	// 执行任务
	if err := task.Execute(p.ctx); err != nil {
		p.logger.Error("任务执行失败",
			zap.String("task_id", task.GetID()),
			zap.Error(err))
	}
}

// handleRejection 处理任务拒绝
func (p *GoroutinePool) handleRejection(task Task) error {
	atomic.AddInt64(&p.rejectedTasks, 1)
	
	switch p.config.RejectPolicy {
	case "caller_runs":
		// 调用者线程执行
		return task.Execute(context.Background())
	case "discard_oldest":
		// 丢弃最老的任务，重新尝试
		select {
		case <-p.taskQueue:
			return p.Submit(task)
		default:
			return fmt.Errorf("任务队列已满，无法丢弃旧任务")
		}
	default:
		return fmt.Errorf("任务队列已满，任务被拒绝: %s", task.GetID())
	}
}

// updateMetrics 更新性能指标
func (p *GoroutinePool) updateMetrics(executionTime time.Duration) {
	p.metrics.Lock()
	defer p.metrics.Unlock()
	
	if p.metrics.avgExecutionTime == 0 {
		p.metrics.avgExecutionTime = executionTime
	} else {
		p.metrics.avgExecutionTime = (p.metrics.avgExecutionTime + executionTime) / 2
	}
	
	if executionTime > p.metrics.maxExecutionTime {
		p.metrics.maxExecutionTime = executionTime
	}
	
	if p.metrics.minExecutionTime == 0 || executionTime < p.metrics.minExecutionTime {
		p.metrics.minExecutionTime = executionTime
	}
	
	p.metrics.lastUpdateTime = time.Now()
}

// GetStats 获取协程池统计信息
func (p *GoroutinePool) GetStats() map[string]interface{} {
	p.metrics.RLock()
	defer p.metrics.RUnlock()
	
	return map[string]interface{}{
		"worker_count":       atomic.LoadInt64(&p.workerCount),
		"active_count":       atomic.LoadInt64(&p.activeCount),
		"queue_size":         len(p.taskQueue),
		"queue_capacity":     cap(p.taskQueue),
		"total_tasks":        atomic.LoadInt64(&p.totalTasks),
		"completed_tasks":    atomic.LoadInt64(&p.completedTasks),
		"rejected_tasks":     atomic.LoadInt64(&p.rejectedTasks),
		"avg_execution_time": p.metrics.avgExecutionTime.String(),
		"max_execution_time": p.metrics.maxExecutionTime.String(),
		"min_execution_time": p.metrics.minExecutionTime.String(),
		"last_update_time":   p.metrics.lastUpdateTime.Format(time.RFC3339),
		"goroutines":         runtime.NumGoroutine(),
	}
}

// monitor 监控协程池状态
func (p *GoroutinePool) monitor() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			stats := p.GetStats()
			p.logger.Info("协程池状态",
				zap.Any("stats", stats))
				
			// 检查是否需要告警
			if queueUsage := float64(stats["queue_size"].(int)) / float64(stats["queue_capacity"].(int)); queueUsage > 0.8 {
				p.logger.Warn("协程池队列使用率过高",
					zap.Float64("usage", queueUsage))
			}
			
		case <-p.ctx.Done():
			return
		}
	}
}

// Shutdown 关闭协程池
func (p *GoroutinePool) Shutdown(timeout time.Duration) error {
	p.cancel()
	
	// 等待所有协程完成
	done := make(chan struct{})
	go func() {
		p.wg.Wait()
		close(done)
	}()
	
	select {
	case <-done:
		p.logger.Info("协程池已优雅关闭")
		return nil
	case <-time.After(timeout):
		return fmt.Errorf("协程池关闭超时")
	}
}
