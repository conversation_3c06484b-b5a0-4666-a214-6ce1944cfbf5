package main

import (
	"fmt"
	"log"
	"strings"

	"walmart-bind-card-processor/internal/config"
	"walmart-bind-card-processor/internal/model"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func main() {
	fmt.Println("🧪 验证process_time修复效果...")

	// 加载配置
	cfg, err := config.LoadConfig("config.yaml")
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 构建数据库连接字符串
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		cfg.Database.User,
		cfg.Database.Password,
		cfg.Database.Host,
		cfg.Database.Port,
		cfg.Database.DBName)

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}

	// 查询最近的绑卡记录，检查process_time准确性
	var records []model.CardRecord
	err = db.Order("created_at DESC").
		Limit(10).
		Find(&records).Error

	if err != nil {
		log.Fatalf("查询绑卡记录失败: %v", err)
	}

	fmt.Printf("\n📊 最近的 %d 条绑卡记录的process_time分析:\n", len(records))
	fmt.Println(strings.Repeat("=", 100))

	accurateCount := 0
	inaccurateCount := 0

	for i, record := range records {
		fmt.Printf("记录 #%d:\n", i+1)
		fmt.Printf("  ID: %s\n", record.ID)
		fmt.Printf("  状态: %s\n", record.Status)
		fmt.Printf("  创建时间: %s\n", record.CreatedAt.Format("2006-01-02 15:04:05.000"))
		fmt.Printf("  更新时间: %s\n", record.UpdatedAt.Format("2006-01-02 15:04:05.000"))
		
		// 计算实际时间差
		actualDuration := record.UpdatedAt.Sub(record.CreatedAt).Seconds()
		fmt.Printf("  实际处理时间: %.3f秒\n", actualDuration)
		
		if record.ProcessTime != nil {
			recordedTime := *record.ProcessTime
			fmt.Printf("  记录的process_time: %.3f秒\n", recordedTime)
			
			// 计算差异（允许1秒的误差）
			diff := actualDuration - recordedTime
			fmt.Printf("  时间差异: %.3f秒\n", diff)
			
			if diff < 1.0 && diff > -1.0 {
				fmt.Printf("  ✅ 时间记录准确\n")
				accurateCount++
			} else {
				fmt.Printf("  ❌ 时间记录不准确（差异: %.3f秒）\n", diff)
				inaccurateCount++
			}
		} else {
			fmt.Printf("  ❌ process_time为空\n")
			inaccurateCount++
		}
		
		fmt.Println("  " + strings.Repeat("-", 80))
	}

	// 总结分析结果
	fmt.Println("\n🎯 process_time准确性分析结果:")
	fmt.Printf("总记录数: %d\n", len(records))
	fmt.Printf("时间记录准确的记录数: %d\n", accurateCount)
	fmt.Printf("时间记录不准确的记录数: %d\n", inaccurateCount)
	
	if len(records) > 0 {
		accuracyRate := float64(accurateCount) / float64(len(records)) * 100
		fmt.Printf("准确率: %.1f%%\n", accuracyRate)
		
		if accuracyRate >= 80.0 {
			fmt.Println("✅ process_time修复效果良好")
		} else {
			fmt.Println("❌ process_time仍存在问题，需要进一步修复")
		}
	}

	// 特别检查最新的记录
	if len(records) > 0 {
		latest := records[0]
		fmt.Println("\n🔍 最新记录详细分析:")
		fmt.Printf("记录ID: %s\n", latest.ID)
		fmt.Printf("创建时间: %s\n", latest.CreatedAt.Format("2006-01-02 15:04:05.000"))
		fmt.Printf("更新时间: %s\n", latest.UpdatedAt.Format("2006-01-02 15:04:05.000"))
		
		actualDuration := latest.UpdatedAt.Sub(latest.CreatedAt).Seconds()
		fmt.Printf("实际处理时间: %.3f秒\n", actualDuration)
		
		if latest.ProcessTime != nil {
			fmt.Printf("记录的process_time: %.3f秒\n", *latest.ProcessTime)
			diff := actualDuration - *latest.ProcessTime
			fmt.Printf("时间差异: %.3f秒\n", diff)
			
			if diff < 1.0 && diff > -1.0 {
				fmt.Println("✅ 最新记录的process_time准确")
			} else {
				fmt.Println("❌ 最新记录的process_time不准确")
				fmt.Println("💡 建议：运行一个新的绑卡测试来验证修复效果")
			}
		} else {
			fmt.Println("❌ 最新记录的process_time为空")
		}
	}
}
