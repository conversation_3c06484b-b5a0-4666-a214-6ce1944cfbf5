package services

import (
	"context"
	"crypto/rand"
	"fmt"
	"math/big"
	"sync"
	"time"

	"walmart-bind-card-processor/internal/config"
	"walmart-bind-card-processor/internal/model"

	"github.com/go-redis/redis/v8"
	"github.com/sony/sonyflake"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// DepartmentWeight 部门权重信息（内存缓存结构）
type DepartmentWeight struct {
	ID               uint    `json:"id"`
	Name             string  `json:"name"`
	BindingWeight    int     `json:"binding_weight"`
	EnableBinding    bool    `json:"enable_binding"`
	AvailableCKCount int64   `json:"available_ck_count"`
	TotalWeight      int     `json:"total_weight"`
	CumulativeWeight int     `json:"cumulative_weight"` // 累积权重，用于O(log n)查找
	LastUpdated      int64   `json:"last_updated"`
}

// CKWeightManager 高性能CK权重管理器
type CKWeightManager struct {
	db          *gorm.DB
	redis       *redis.Client
	logger      *zap.Logger
	snowflake   *sonyflake.Sonyflake
	
	// 内存缓存
	weightCache     map[uint][]*DepartmentWeight // merchantID -> departments
	cacheMutex      sync.RWMutex
	cacheExpiry     time.Duration
	lastCacheUpdate map[uint]time.Time
	
	// 配置
	config struct {
		CacheRefreshInterval time.Duration
		LockTimeout         time.Duration
		MaxRetries          int
		CircuitBreakerThreshold int
	}
	
	// 熔断器
	circuitBreaker map[uint]*CircuitBreaker
	cbMutex        sync.RWMutex
}

// CircuitBreaker 熔断器
type CircuitBreaker struct {
	FailureCount    int
	LastFailureTime time.Time
	State          string // "CLOSED", "OPEN", "HALF_OPEN"
	Threshold      int
	Timeout        time.Duration
}

// NewCKWeightManager 创建高性能CK权重管理器
func NewCKWeightManager(db *gorm.DB, redis *redis.Client, logger *zap.Logger, config *config.WeightAlgorithmConfig) *CKWeightManager {
	snowflake := sonyflake.NewSonyflake(sonyflake.Settings{})

	manager := &CKWeightManager{
		db:              db,
		redis:           redis,
		logger:          logger,
		snowflake:       snowflake,
		weightCache:     make(map[uint][]*DepartmentWeight),
		lastCacheUpdate: make(map[uint]time.Time),
		cacheExpiry:     5 * time.Minute,
		circuitBreaker:  make(map[uint]*CircuitBreaker),
	}

	// 使用配置文件中的值
	manager.config.CacheRefreshInterval = config.CacheRefreshInterval
	manager.config.LockTimeout = config.LockTimeout
	manager.config.MaxRetries = config.MaxRetries
	manager.config.CircuitBreakerThreshold = config.CircuitBreakerThreshold

	// 启动后台缓存刷新
	go manager.startCacheRefreshWorker()

	return manager
}

// SelectDepartmentByWeight 基于权重算法选择部门（O(log n)时间复杂度）
func (m *CKWeightManager) SelectDepartmentByWeight(ctx context.Context, merchantID uint) (*model.Department, error) {
	startTime := time.Now()
	defer func() {
		m.logger.Debug("权重选择耗时", 
			zap.Uint("merchant_id", merchantID),
			zap.Duration("duration", time.Since(startTime)))
	}()
	
	// 检查熔断器
	if m.isCircuitBreakerOpen(merchantID) {
		return nil, fmt.Errorf("熔断器开启，商户 %d 暂时不可用", merchantID)
	}
	
	// 获取权重信息（优先从缓存）
	departments, err := m.getDepartmentWeights(ctx, merchantID)
	if err != nil {
		m.recordFailure(merchantID)
		return nil, fmt.Errorf("获取部门权重失败: %w", err)
	}
	
	if len(departments) == 0 {
		return nil, fmt.Errorf("商户没有可用CK")
	}
	
	// 使用加密安全的随机数生成
	totalWeight := departments[len(departments)-1].CumulativeWeight
	randomBig, err := rand.Int(rand.Reader, big.NewInt(int64(totalWeight)))
	if err != nil {
		m.recordFailure(merchantID)
		return nil, fmt.Errorf("生成随机数失败: %w", err)
	}
	randomValue := int(randomBig.Int64())
	
	// 二分查找选择部门（O(log n)）
	selectedDept := m.binarySearchDepartment(departments, randomValue)
	if selectedDept == nil {
		m.recordFailure(merchantID)
		return nil, fmt.Errorf("权重算法选择失败")
	}
	
	// 查询完整的部门信息
	var dept model.Department
	err = m.db.WithContext(ctx).First(&dept, selectedDept.ID).Error
	if err != nil {
		m.recordFailure(merchantID)
		return nil, fmt.Errorf("查询部门详情失败: %w", err)
	}
	
	m.recordSuccess(merchantID)
	
	m.logger.Info("权重算法选择部门成功",
		zap.Uint("merchant_id", merchantID),
		zap.Uint("department_id", dept.ID),
		zap.String("department_name", dept.Name),
		zap.Int("binding_weight", selectedDept.BindingWeight),
		zap.Int("random_value", randomValue),
		zap.Int("total_weight", totalWeight))
	
	return &dept, nil
}

// getDepartmentWeights 获取部门权重信息（实时查询，确保CK数据最新）
func (m *CKWeightManager) getDepartmentWeights(ctx context.Context, merchantID uint) ([]*DepartmentWeight, error) {
	// 🔧 关键修复：移除缓存，直接从数据库实时查询
	// 确保用户添加CK后立即可用
	m.logger.Debug("实时查询权重数据（确保CK数据最新）", zap.Uint("merchant_id", merchantID))
	return m.refreshDepartmentWeights(ctx, merchantID)
}

// refreshDepartmentWeights 从数据库刷新部门权重信息
func (m *CKWeightManager) refreshDepartmentWeights(ctx context.Context, merchantID uint) ([]*DepartmentWeight, error) {
	m.logger.Debug("从数据库刷新权重数据", zap.Uint("merchant_id", merchantID))

	// 查询启用绑卡的部门
	var departments []model.Department
	err := m.db.WithContext(ctx).Where(
		"merchant_id = ? AND status = ? AND enable_binding = ? AND binding_weight > 0",
		merchantID, true, true,
	).Find(&departments).Error

	if err != nil {
		return nil, fmt.Errorf("查询部门失败: %w", err)
	}


	// 构建权重信息并计算可用CK数量
	var weights []*DepartmentWeight
	cumulativeWeight := 0
	totalDepartments := len(departments)
	departmentsWithCK := 0

	for _, dept := range departments {
		// 查询该部门的可用CK数量
		var availableCKCount int64
		var totalCKCount int64
		var activeCKCount int64
		var notDeletedCKCount int64

		// 统计总CK数量
		m.db.WithContext(ctx).Model(&model.WalmartCK{}).Where("department_id = ?", dept.ID).Count(&totalCKCount)

		// 统计激活的CK数量
		m.db.WithContext(ctx).Model(&model.WalmartCK{}).Where("department_id = ? AND active = ?", dept.ID, true).Count(&activeCKCount)

		// 统计未删除的CK数量
		m.db.WithContext(ctx).Model(&model.WalmartCK{}).Where("department_id = ? AND is_deleted = ?", dept.ID, false).Count(&notDeletedCKCount)

		// 查询可用CK数量（满足所有条件）
		m.db.WithContext(ctx).Model(&model.WalmartCK{}).Where(
			"department_id = ? AND active = ? AND is_deleted = ? AND bind_count < total_limit",
			dept.ID, true, false,
		).Count(&availableCKCount)

		// 只有有可用CK的部门才参与权重分配
		if availableCKCount > 0 {
			departmentsWithCK++
			cumulativeWeight += dept.BindingWeight

			weight := &DepartmentWeight{
				ID:               dept.ID,
				Name:             dept.Name,
				BindingWeight:    dept.BindingWeight,
				EnableBinding:    dept.EnableBinding,
				AvailableCKCount: availableCKCount,
				CumulativeWeight: cumulativeWeight,
				LastUpdated:      time.Now().Unix(),
			}
			weights = append(weights, weight)
		}
	}

	m.logger.Info("权重数据实时查询完成（无缓存，确保CK数据最新）",
		zap.Uint("merchant_id", merchantID),
		zap.Int("total_departments", totalDepartments),
		zap.Int("departments_with_ck", departmentsWithCK),
		zap.Int("department_count", len(weights)),
		zap.Int("total_weight", cumulativeWeight))

	// 🔧 关键修复：移除缓存更新逻辑
	// 确保每次都从数据库获取最新的CK数据
	
	return weights, nil
}

// binarySearchDepartment 二分查找选择部门（O(log n)）
func (m *CKWeightManager) binarySearchDepartment(departments []*DepartmentWeight, randomValue int) *DepartmentWeight {
	left, right := 0, len(departments)-1
	
	for left <= right {
		mid := (left + right) / 2
		
		if randomValue < departments[mid].CumulativeWeight {
			if mid == 0 || randomValue >= departments[mid-1].CumulativeWeight {
				return departments[mid]
			}
			right = mid - 1
		} else {
			left = mid + 1
		}
	}
	
	// 兜底返回最后一个部门
	if len(departments) > 0 {
		return departments[len(departments)-1]
	}
	
	return nil
}

// startCacheRefreshWorker 启动后台缓存刷新工作器
func (m *CKWeightManager) startCacheRefreshWorker() {
	ticker := time.NewTicker(m.config.CacheRefreshInterval)
	defer ticker.Stop()
	
	for range ticker.C {
		m.cacheMutex.RLock()
		merchantIDs := make([]uint, 0, len(m.weightCache))
		for merchantID := range m.weightCache {
			merchantIDs = append(merchantIDs, merchantID)
		}
		m.cacheMutex.RUnlock()
		
		// 异步刷新每个商户的缓存
		for _, merchantID := range merchantIDs {
			go func(mid uint) {
				ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
				defer cancel()
				
				_, err := m.refreshDepartmentWeights(ctx, mid)
				if err != nil {
					m.logger.Error("后台刷新权重缓存失败",
						zap.Uint("merchant_id", mid),
						zap.Error(err))
				}
			}(merchantID)
		}
	}
}

// 熔断器相关方法
func (m *CKWeightManager) isCircuitBreakerOpen(merchantID uint) bool {
	m.cbMutex.RLock()
	cb, exists := m.circuitBreaker[merchantID]
	m.cbMutex.RUnlock()
	
	if !exists {
		return false
	}
	
	if cb.State == "OPEN" {
		if time.Since(cb.LastFailureTime) > cb.Timeout {
			// 尝试半开状态
			m.cbMutex.Lock()
			cb.State = "HALF_OPEN"
			m.cbMutex.Unlock()
			return false
		}
		return true
	}
	
	return false
}

func (m *CKWeightManager) recordFailure(merchantID uint) {
	m.cbMutex.Lock()
	defer m.cbMutex.Unlock()
	
	cb, exists := m.circuitBreaker[merchantID]
	if !exists {
		cb = &CircuitBreaker{
			Threshold: m.config.CircuitBreakerThreshold,
			Timeout:   30 * time.Second,
			State:     "CLOSED",
		}
		m.circuitBreaker[merchantID] = cb
	}
	
	cb.FailureCount++
	cb.LastFailureTime = time.Now()
	
	if cb.FailureCount >= cb.Threshold {
		cb.State = "OPEN"
		m.logger.Warn("熔断器开启",
			zap.Uint("merchant_id", merchantID),
			zap.Int("failure_count", cb.FailureCount))
	}
}

func (m *CKWeightManager) recordSuccess(merchantID uint) {
	m.cbMutex.Lock()
	defer m.cbMutex.Unlock()
	
	cb, exists := m.circuitBreaker[merchantID]
	if exists {
		cb.FailureCount = 0
		cb.State = "CLOSED"
	}
}

// GetDepartmentWeightStats 获取部门权重统计信息
func (m *CKWeightManager) GetDepartmentWeightStats(ctx context.Context, merchantID uint) (map[string]interface{}, error) {
	departments, err := m.getDepartmentWeights(ctx, merchantID)
	if err != nil {
		return nil, err
	}
	
	stats := map[string]interface{}{
		"merchant_id":      merchantID,
		"department_count": len(departments),
		"departments":      departments,
		"cache_hit":        true,
		"timestamp":        time.Now().Unix(),
	}
	
	if len(departments) > 0 {
		stats["total_weight"] = departments[len(departments)-1].CumulativeWeight
	}
	
	return stats, nil
}
