"""
异步余额重试服务

用于处理绑卡成功但余额获取失败的情况，在后台异步重试获取余额，
避免阻塞主要的回调处理流程。
"""

import asyncio
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session

from app.core.logging import get_logger
from app.crud import card as card_crud
from app.crud.card import get_card_record_async
from app.models.card_record import CardRecord
from app.services.walmart_api_service import WalmartAPIService
from app.core.walmart_api import WalmartAPI
from app.models.walmart_ck import WalmartCK
from sqlalchemy import select
from app.utils.queue_producer import send_callback_task
from app.db.session import AsyncSessionLocal

logger = get_logger(__name__)

# 配置常量
MAX_BALANCE_RETRY_ATTEMPTS = 3
BALANCE_RETRY_DELAYS = [30, 120, 300]  # 30秒, 2分钟, 5分钟
BALANCE_FETCH_TIMEOUT = 10  # 10秒超时


class AsyncBalanceRetryService:
    """异步余额重试服务"""
    
    def __init__(self):
        self.api_service = WalmartAPIService()
        self._retry_tasks: Dict[str, asyncio.Task] = {}
    
    async def schedule_balance_retry(
        self, 
        record_id: int, 
        merchant_id: int, 
        ext_data: Optional[str] = None,
        trace_id: Optional[str] = None
    ):
        """
        安排余额重试任务
        
        Args:
            record_id: 记录ID
            merchant_id: 商户ID
            ext_data: 扩展数据
            trace_id: 追踪ID
        """
        task_key = f"{record_id}_{merchant_id}"
        
        # 如果已经有重试任务在运行，取消它
        if task_key in self._retry_tasks:
            self._retry_tasks[task_key].cancel()
        
        # 创建新的重试任务
        task = asyncio.create_task(
            self._execute_balance_retry(record_id, merchant_id, ext_data, trace_id)
        )
        self._retry_tasks[task_key] = task
        
        logger.info(
            f"[BALANCE_RETRY_SCHEDULED] 已安排余额重试任务 | "
            f"record_id={record_id} | trace_id={trace_id}"
        )
        
        # 任务完成后清理
        task.add_done_callback(lambda t: self._cleanup_task(task_key))
    
    def _cleanup_task(self, task_key: str):
        """清理完成的任务"""
        if task_key in self._retry_tasks:
            del self._retry_tasks[task_key]
    
    async def _execute_balance_retry(
        self, 
        record_id: int, 
        merchant_id: int, 
        ext_data: Optional[str],
        trace_id: Optional[str]
    ):
        """
        执行余额重试逻辑
        
        Args:
            record_id: 记录ID
            merchant_id: 商户ID
            ext_data: 扩展数据
            trace_id: 追踪ID
        """
        for attempt in range(MAX_BALANCE_RETRY_ATTEMPTS):
            try:
                # 等待重试延迟
                if attempt > 0:
                    delay = BALANCE_RETRY_DELAYS[attempt - 1]
                    logger.info(
                        f"[BALANCE_RETRY_WAIT] 等待 {delay} 秒后重试余额获取 | "
                        f"record_id={record_id} | attempt={attempt + 1}"
                    )
                    await asyncio.sleep(delay)
                
                # 尝试获取余额
                success = await self._try_fetch_balance(record_id, merchant_id, trace_id)
                
                if success:
                    logger.info(
                        f"[BALANCE_RETRY_SUCCESS] 余额重试成功 | "
                        f"record_id={record_id} | attempt={attempt + 1}"
                    )
                    
                    # 余额获取成功，重新发送回调
                    await self._resend_callback(record_id, merchant_id, ext_data, trace_id)
                    return
                else:
                    logger.warning(
                        f"[BALANCE_RETRY_FAILED] 余额重试失败 | "
                        f"record_id={record_id} | attempt={attempt + 1}"
                    )
            
            except asyncio.CancelledError:
                logger.info(f"[BALANCE_RETRY_CANCELLED] 余额重试任务被取消 | record_id={record_id}")
                return
            except Exception as e:
                logger.error(
                    f"[BALANCE_RETRY_ERROR] 余额重试异常 | "
                    f"record_id={record_id} | attempt={attempt + 1} | error={e}"
                )
        
        # 所有重试都失败了
        logger.error(
            f"[BALANCE_RETRY_EXHAUSTED] 余额重试次数用尽 | "
            f"record_id={record_id} | max_attempts={MAX_BALANCE_RETRY_ATTEMPTS}"
        )
    
    async def _try_fetch_balance(
        self, 
        record_id: int, 
        merchant_id: int, 
        trace_id: Optional[str]
    ) -> bool:
        """
        尝试获取余额
        
        Args:
            record_id: 记录ID
            merchant_id: 商户ID
            trace_id: 追踪ID
            
        Returns:
            bool: 是否成功获取余额
        """
        try:
            # 使用独立的异步数据库会话
            async with AsyncSessionLocal() as db:
                # 获取记录
                record = await get_card_record_async(db, str(record_id), merchant_id)
                if not record:
                    logger.error(f"[BALANCE_RETRY_NO_RECORD] 记录不存在 | record_id={record_id}")
                    return False
                
                # 检查记录状态
                if record.status != "success":
                    logger.warning(
                        f"[BALANCE_RETRY_INVALID_STATUS] 记录状态不是成功，跳过余额重试 | "
                        f"record_id={record_id} | status={record.status}"
                    )
                    return False
                
                # 检查是否已经有余额
                if (record.balance is not None or 
                    record.cardBalance is not None or 
                    record.balanceCnt is not None):
                    logger.info(
                        f"[BALANCE_RETRY_ALREADY_EXISTS] 余额已存在，无需重试 | "
                        f"record_id={record_id}"
                    )
                    return True
                
                # 尝试获取余额
                balance_result = await asyncio.wait_for(
                    self._fetch_balance_async(record),
                    timeout=BALANCE_FETCH_TIMEOUT
                )
                
                if balance_result:
                    # 更新记录
                    record.balance = balance_result.get('balance')
                    record.cardBalance = balance_result.get('cardBalance')
                    record.balanceCnt = balance_result.get('balanceCnt')
                    record.updated_at = datetime.now()
                    
                    # 异步提交数据库
                    await db.commit()
                    
                    logger.info(
                        f"[BALANCE_RETRY_UPDATED] 余额信息已更新 | "
                        f"record_id={record_id} | balance={record.balance}"
                    )
                    return True
                else:
                    logger.warning(f"[BALANCE_RETRY_NO_DATA] 未获取到余额数据 | record_id={record_id}")
                    return False
                    
        except asyncio.TimeoutError:
            logger.warning(
                f"[BALANCE_RETRY_TIMEOUT] 余额获取超时 | "
                f"record_id={record_id} | timeout={BALANCE_FETCH_TIMEOUT}s"
            )
            return False
        except Exception as e:
            logger.error(f"[BALANCE_RETRY_EXCEPTION] 余额获取异常 | record_id={record_id} | error={e}")
            return False
    
    async def _fetch_balance_async(self, record: CardRecord) -> Optional[Dict[str, Any]]:
        """
        异步获取余额

        Args:
            record: 卡记录

        Returns:
            Optional[Dict[str, Any]]: 余额数据或None
        """
        try:
            # 检查记录中是否有walmart_ck_id
            if not record.walmart_ck_id:
                logger.error(f"[BALANCE_FETCH_ERROR] 记录中缺少walmart_ck_id | record_id={record.id}")
                return None

            # 获取沃尔玛CK配置
            async with AsyncSessionLocal() as db:
                stmt = select(WalmartCK).where(WalmartCK.id == record.walmart_ck_id)
                result = await db.execute(stmt)
                walmart_ck = result.scalar_one_or_none()

                if not walmart_ck:
                    logger.error(f"[BALANCE_FETCH_ERROR] 找不到沃尔玛CK配置 | walmart_ck_id={record.walmart_ck_id}")
                    return None

                if not walmart_ck.active:
                    logger.warning(f"[BALANCE_FETCH_ERROR] 沃尔玛CK已被禁用 | walmart_ck_id={record.walmart_ck_id}")
                    return None

            # 准备API参数
            api_params = self._prepare_api_request_params(walmart_ck)
            if not api_params:
                logger.error(f"[BALANCE_FETCH_ERROR] 准备API参数失败 | walmart_ck_id={record.walmart_ck_id}")
                return None

            # 创建沃尔玛API实例
            walmart_api = WalmartAPI.create_legacy(
                base_url="https://apicard.swiftpass.cn",
                encryption_key=api_params["encryption_key"],
                token=None,
                version=api_params["version"],
                sign=api_params["sign"]
            )

            # 异步获取余额（在线程池中执行同步API调用）
            loop = asyncio.get_event_loop()
            balance_data = await loop.run_in_executor(
                None,
                walmart_api.get_card_balance_sync,
                record.card_number,
                False  # 不使用debug模式，获取真实数据
            )

            logger.info(
                f"[BALANCE_FETCH_SUCCESS] 余额获取成功 | "
                f"record_id={record.id} | balance={balance_data.get('balance')} | "
                f"cardBalance={balance_data.get('cardBalance')} | balanceCnt={balance_data.get('balanceCnt')}"
            )

            return balance_data

        except Exception as e:
            logger.error(f"[BALANCE_FETCH_EXCEPTION] 余额获取异常 | record_id={record.id} | error={e}")
            return None

    def _prepare_api_request_params(self, walmart_ck: WalmartCK) -> Optional[Dict[str, Any]]:
        """准备API请求参数"""
        user_sign_raw = walmart_ck.sign
        sign_parts = user_sign_raw.split("#")
        if len(sign_parts) < 3:
            logger.error(f"沃尔玛CK {walmart_ck.id} 签名格式错误: {user_sign_raw}")
            return None

        return {
            "sign": sign_parts[0],
            "encryption_key": sign_parts[1],
            "version": sign_parts[2],
        }

    async def _resend_callback(
        self, 
        record_id: int, 
        merchant_id: int, 
        ext_data: Optional[str],
        trace_id: Optional[str]
    ):
        """
        重新发送回调
        
        Args:
            record_id: 记录ID
            merchant_id: 商户ID
            ext_data: 扩展数据
            trace_id: 追踪ID
        """
        try:
            await send_callback_task({
                "record_id": record_id,
                "merchant_id": merchant_id,
                "retry_count": 0,  # 重置重试次数
                "ext_data": ext_data,
                "trace_id": trace_id,
            })
            
            logger.info(
                f"[BALANCE_RETRY_CALLBACK_SENT] 余额重试成功后重新发送回调 | "
                f"record_id={record_id} | trace_id={trace_id}"
            )
        except Exception as e:
            logger.error(
                f"[BALANCE_RETRY_CALLBACK_ERROR] 重新发送回调失败 | "
                f"record_id={record_id} | error={e}"
            )
    

    
    async def cancel_all_tasks(self):
        """取消所有重试任务"""
        for task in self._retry_tasks.values():
            task.cancel()
        
        # 等待所有任务完成
        if self._retry_tasks:
            await asyncio.gather(*self._retry_tasks.values(), return_exceptions=True)
        
        self._retry_tasks.clear()
        logger.info("[BALANCE_RETRY_CLEANUP] 所有余额重试任务已取消")


# 全局实例
async_balance_retry_service = AsyncBalanceRetryService()
