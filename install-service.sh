#!/bin/bash

# Walmart Card Binding Server - Service Installation Script
# 沃尔玛绑卡服务安装脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SERVICE_NAME="walmart-bind-card"
SERVICE_USER="walmart"
SERVICE_GROUP="walmart"
INSTALL_DIR="/opt/walmart-bind-card-server"
LOG_DIR="/var/log/walmart-bind-card"
DATA_DIR="/opt/walmart-bind-card-server/data"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 创建服务用户
create_service_user() {
    log_step "创建服务用户和组..."
    
    if ! getent group "$SERVICE_GROUP" > /dev/null 2>&1; then
        groupadd --system "$SERVICE_GROUP"
        log_info "创建组: $SERVICE_GROUP"
    else
        log_info "组已存在: $SERVICE_GROUP"
    fi
    
    if ! getent passwd "$SERVICE_USER" > /dev/null 2>&1; then
        useradd --system --gid "$SERVICE_GROUP" --home-dir "$INSTALL_DIR" \
                --shell /bin/false --comment "Walmart Card Binding Service" "$SERVICE_USER"
        log_info "创建用户: $SERVICE_USER"
    else
        log_info "用户已存在: $SERVICE_USER"
    fi
}

# 创建目录结构
create_directories() {
    log_step "创建目录结构..."
    
    # 创建安装目录
    mkdir -p "$INSTALL_DIR"
    mkdir -p "$INSTALL_DIR/logs"
    mkdir -p "$DATA_DIR"
    mkdir -p "$LOG_DIR"
    
    # 设置权限
    chown -R "$SERVICE_USER:$SERVICE_GROUP" "$INSTALL_DIR"
    chown -R "$SERVICE_USER:$SERVICE_GROUP" "$LOG_DIR"
    
    chmod 755 "$INSTALL_DIR"
    chmod 755 "$LOG_DIR"
    chmod 755 "$DATA_DIR"
    
    log_info "目录创建完成"
}

# 安装systemd服务
install_systemd_service() {
    log_step "安装systemd服务..."
    
    if [[ ! -f "walmart-bind-card.service" ]]; then
        log_error "服务文件 walmart-bind-card.service 不存在"
        exit 1
    fi
    
    # 复制服务文件
    cp walmart-bind-card.service /etc/systemd/system/
    
    # 重新加载systemd
    systemctl daemon-reload
    
    log_info "systemd服务安装完成"
}

# 启用并启动服务
enable_and_start_service() {
    log_step "启用并启动服务..."
    
    # 启用服务（开机自启）
    systemctl enable "$SERVICE_NAME"
    log_info "服务已设置为开机自启"
    
    # 启动服务
    if systemctl start "$SERVICE_NAME"; then
        log_info "服务启动成功"
    else
        log_error "服务启动失败"
        log_info "查看日志: journalctl -u $SERVICE_NAME -f"
        exit 1
    fi
}

# 检查服务状态
check_service_status() {
    log_step "检查服务状态..."
    
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_info "✅ 服务运行正常"
        systemctl status "$SERVICE_NAME" --no-pager -l
    else
        log_warn "⚠️  服务未运行"
        systemctl status "$SERVICE_NAME" --no-pager -l
    fi
}

# 创建管理脚本
create_management_scripts() {
    log_step "创建管理脚本..."
    
    # 创建服务管理脚本
    cat > /usr/local/bin/walmart-service << 'EOF'
#!/bin/bash

SERVICE_NAME="walmart-bind-card"

case "$1" in
    start)
        echo "启动 Walmart Card Binding 服务..."
        systemctl start $SERVICE_NAME
        ;;
    stop)
        echo "停止 Walmart Card Binding 服务..."
        systemctl stop $SERVICE_NAME
        ;;
    restart)
        echo "重启 Walmart Card Binding 服务..."
        systemctl restart $SERVICE_NAME
        ;;
    status)
        systemctl status $SERVICE_NAME
        ;;
    logs)
        journalctl -u $SERVICE_NAME -f
        ;;
    enable)
        systemctl enable $SERVICE_NAME
        echo "服务已设置为开机自启"
        ;;
    disable)
        systemctl disable $SERVICE_NAME
        echo "服务已取消开机自启"
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|logs|enable|disable}"
        exit 1
        ;;
esac
EOF

    chmod +x /usr/local/bin/walmart-service
    log_info "管理脚本创建完成: /usr/local/bin/walmart-service"
}

# 创建日志轮转配置
create_logrotate_config() {
    log_step "创建日志轮转配置..."
    
    cat > /etc/logrotate.d/walmart-bind-card << EOF
$LOG_DIR/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $SERVICE_USER $SERVICE_GROUP
    postrotate
        systemctl reload $SERVICE_NAME > /dev/null 2>&1 || true
    endscript
}
EOF

    log_info "日志轮转配置创建完成"
}

# 显示安装后信息
show_post_install_info() {
    log_step "安装完成！"
    
    echo ""
    echo "=========================================="
    echo "  Walmart Card Binding Server 服务信息"
    echo "=========================================="
    echo "服务名称: $SERVICE_NAME"
    echo "安装目录: $INSTALL_DIR"
    echo "日志目录: $LOG_DIR"
    echo "服务用户: $SERVICE_USER"
    echo ""
    echo "常用命令:"
    echo "  启动服务: walmart-service start"
    echo "  停止服务: walmart-service stop"
    echo "  重启服务: walmart-service restart"
    echo "  查看状态: walmart-service status"
    echo "  查看日志: walmart-service logs"
    echo ""
    echo "或者使用systemctl:"
    echo "  systemctl start $SERVICE_NAME"
    echo "  systemctl stop $SERVICE_NAME"
    echo "  systemctl restart $SERVICE_NAME"
    echo "  systemctl status $SERVICE_NAME"
    echo "  journalctl -u $SERVICE_NAME -f"
    echo ""
    echo "注意事项:"
    echo "1. 请确保将您的执行文件放置在: $INSTALL_DIR/"
    echo "2. 如果是Python脚本，请修改服务文件中的ExecStart路径"
    echo "3. 服务已设置为开机自启动"
    echo "4. 服务会在异常退出时自动重启"
    echo "=========================================="
}

# 主函数
main() {
    log_info "开始安装 Walmart Card Binding Server 服务..."
    
    check_root
    create_service_user
    create_directories
    install_systemd_service
    create_management_scripts
    create_logrotate_config
    enable_and_start_service
    check_service_status
    show_post_install_info
}

# 执行主函数
main "$@"
