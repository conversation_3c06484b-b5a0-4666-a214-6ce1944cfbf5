# 沃尔玛绑卡系统并发测试工具 Makefile

# 变量定义
BINARY_NAME=walmart-test
MAIN_FILE=main.go
BUILD_DIR=build
VERSION=$(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
BUILD_TIME=$(shell date +%Y-%m-%d_%H:%M:%S)
LDFLAGS=-ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME)"

# 默认目标
.PHONY: all
all: clean build

# 编译
.PHONY: build
build:
	@echo "🔨 编译 $(BINARY_NAME)..."
	@go mod tidy
	@go build $(LDFLAGS) -o $(BINARY_NAME) $(MAIN_FILE)
	@echo "✅ 编译完成: $(BINARY_NAME)"

# 交叉编译
.PHONY: build-all
build-all: clean
	@echo "🔨 交叉编译所有平台..."
	@mkdir -p $(BUILD_DIR)
	
	@echo "编译 Linux amd64..."
	@GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-linux-amd64 $(MAIN_FILE)
	
	@echo "编译 Windows amd64..."
	@GOOS=windows GOARCH=amd64 go build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-windows-amd64.exe $(MAIN_FILE)
	
	@echo "编译 macOS amd64..."
	@GOOS=darwin GOARCH=amd64 go build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-darwin-amd64 $(MAIN_FILE)
	
	@echo "编译 macOS arm64..."
	@GOOS=darwin GOARCH=arm64 go build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-darwin-arm64 $(MAIN_FILE)
	
	@echo "✅ 交叉编译完成，文件保存在 $(BUILD_DIR)/ 目录"

# 清理
.PHONY: clean
clean:
	@echo "🧹 清理编译文件..."
	@rm -f $(BINARY_NAME)
	@rm -rf $(BUILD_DIR)
	@rm -f test_results_*.json
	@rm -f test_suite_report_*.txt
	@echo "✅ 清理完成"

# 安装依赖
.PHONY: deps
deps:
	@echo "📦 安装依赖..."
	@go mod download
	@go mod tidy
	@echo "✅ 依赖安装完成"

# 运行测试
.PHONY: test
test: build
	@echo "🧪 运行Go单元测试..."
	@go test -v ./...
	@echo "✅ 单元测试完成"

# 代码格式化
.PHONY: fmt
fmt:
	@echo "🎨 格式化代码..."
	@go fmt ./...
	@echo "✅ 代码格式化完成"

# 代码检查
.PHONY: lint
lint:
	@echo "🔍 代码检查..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "⚠️ golangci-lint 未安装，跳过代码检查"; \
		echo "安装命令: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest"; \
	fi
	@echo "✅ 代码检查完成"

# 快速测试（需要提供参数）
.PHONY: quick-test
quick-test: build
	@if [ -z "$(API_KEY)" ] || [ -z "$(API_SECRET)" ] || [ -z "$(MERCHANT)" ]; then \
		echo "❌ 缺少必需参数"; \
		echo "使用方法: make quick-test API_KEY=your_key API_SECRET=your_secret MERCHANT=your_merchant [URL=http://localhost:8000]"; \
		exit 1; \
	fi
	@echo "🚀 运行快速测试..."
	@./$(BINARY_NAME) \
		--url "$(or $(URL),http://localhost:8000)" \
		--key "$(API_KEY)" \
		--secret "$(API_SECRET)" \
		--merchant "$(MERCHANT)" \
		--concurrency 5 \
		--requests 25

# 完整测试套件（需要提供参数）
.PHONY: full-test
full-test: build
	@if [ -z "$(API_KEY)" ] || [ -z "$(API_SECRET)" ] || [ -z "$(MERCHANT)" ]; then \
		echo "❌ 缺少必需参数"; \
		echo "使用方法: make full-test API_KEY=your_key API_SECRET=your_secret MERCHANT=your_merchant [URL=http://localhost:8000]"; \
		exit 1; \
	fi
	@echo "🚀 运行完整测试套件..."
	@chmod +x test_scenarios.sh
	@./test_scenarios.sh "$(API_KEY)" "$(API_SECRET)" "$(MERCHANT)" "$(or $(URL),http://localhost:8000)"

# 安装到系统
.PHONY: install
install: build
	@echo "📦 安装到系统..."
	@sudo cp $(BINARY_NAME) /usr/local/bin/
	@echo "✅ 安装完成，可以使用 $(BINARY_NAME) 命令"

# 卸载
.PHONY: uninstall
uninstall:
	@echo "🗑️ 从系统卸载..."
	@sudo rm -f /usr/local/bin/$(BINARY_NAME)
	@echo "✅ 卸载完成"

# 创建发布包
.PHONY: release
release: build-all
	@echo "📦 创建发布包..."
	@mkdir -p $(BUILD_DIR)/release
	
	@for binary in $(BUILD_DIR)/$(BINARY_NAME)-*; do \
		if [ -f "$$binary" ]; then \
			basename=$$(basename $$binary); \
			platform=$$(echo $$basename | sed 's/$(BINARY_NAME)-//'); \
			echo "打包 $$platform..."; \
			mkdir -p $(BUILD_DIR)/release/$$platform; \
			cp $$binary $(BUILD_DIR)/release/$$platform/; \
			cp README.md $(BUILD_DIR)/release/$$platform/; \
			cp config.example.json $(BUILD_DIR)/release/$$platform/; \
			cp test_scenarios.sh $(BUILD_DIR)/release/$$platform/; \
			cd $(BUILD_DIR)/release && tar -czf $(BINARY_NAME)-$$platform.tar.gz $$platform/; \
			cd ../..; \
		fi \
	done
	
	@echo "✅ 发布包创建完成，保存在 $(BUILD_DIR)/release/ 目录"

# 显示帮助
.PHONY: help
help:
	@echo "沃尔玛绑卡系统并发测试工具 - 可用命令:"
	@echo ""
	@echo "编译相关:"
	@echo "  make build        - 编译当前平台的二进制文件"
	@echo "  make build-all    - 交叉编译所有平台的二进制文件"
	@echo "  make clean        - 清理编译文件和测试结果"
	@echo ""
	@echo "开发相关:"
	@echo "  make deps         - 安装Go依赖"
	@echo "  make test         - 运行Go单元测试"
	@echo "  make fmt          - 格式化代码"
	@echo "  make lint         - 代码检查"
	@echo ""
	@echo "测试相关:"
	@echo "  make quick-test   - 运行快速测试 (需要API参数)"
	@echo "  make full-test    - 运行完整测试套件 (需要API参数)"
	@echo ""
	@echo "部署相关:"
	@echo "  make install      - 安装到系统 (/usr/local/bin)"
	@echo "  make uninstall    - 从系统卸载"
	@echo "  make release      - 创建发布包"
	@echo ""
	@echo "参数示例:"
	@echo "  make quick-test API_KEY=your_key API_SECRET=your_secret MERCHANT=your_merchant"
	@echo "  make full-test API_KEY=your_key API_SECRET=your_secret MERCHANT=your_merchant URL=http://localhost:8000"
	@echo ""
	@echo "其他:"
	@echo "  make help         - 显示此帮助信息"
