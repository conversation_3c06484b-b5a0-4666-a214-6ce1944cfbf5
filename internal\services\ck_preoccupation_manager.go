package services

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"time"

	"walmart-bind-card-processor/internal/config"
	"walmart-bind-card-processor/internal/model"

	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// CKPreoccupationRecord CK预占用记录
type CKPreoccupationRecord struct {
	CKID         uint      `json:"ck_id"`
	MerchantID   uint      `json:"merchant_id"`
	DepartmentID uint      `json:"department_id"`
	TraceID      string    `json:"trace_id"`
	RecordID     string    `json:"record_id"`
	Amount       int       `json:"amount"`
	CardNumber   string    `json:"card_number"`
	Status       string    `json:"status"` // "PREOCCUPIED", "COMMITTED", "ROLLBACK"
	CreatedAt    time.Time `json:"created_at"`
	ExpiredAt    time.Time `json:"expired_at"`
}

// CKPreoccupationManager CK预占用管理器
type CKPreoccupationManager struct {
	db     *gorm.DB
	redis  *redis.Client
	logger *zap.Logger
	
	// 配置
	preoccupationTimeout time.Duration // 预占用超时时间
	lockTimeout         time.Duration // 分布式锁超时时间
}

// NewCKPreoccupationManager 创建CK预占用管理器
func NewCKPreoccupationManager(db *gorm.DB, redis *redis.Client, logger *zap.Logger, config *config.PreoccupationConfig, lockConfig *config.DistributedLockConfig) *CKPreoccupationManager {
	manager := &CKPreoccupationManager{
		db:                   db,
		redis:               redis,
		logger:               logger,
		preoccupationTimeout: config.Timeout,
		lockTimeout:         lockConfig.Timeout,
	}

	// 启动清理过期预占用的后台任务
	go manager.startCleanupWorker()

	return manager
}

// PreoccupyCK 预占用CK（幂等操作）
func (m *CKPreoccupationManager) PreoccupyCK(ctx context.Context, req *CKPreoccupationRequest) (*CKPreoccupationRecord, error) {
	// 生成幂等性键
	idempotencyKey := fmt.Sprintf("preoccupy:%s:%s", req.TraceID, req.RecordID)
	
	// 检查是否已经预占用（幂等性）
	existingRecord, err := m.getPreoccupationRecord(ctx, idempotencyKey)
	if err == nil && existingRecord != nil {
		m.logger.Info("CK已预占用（幂等返回）",
			zap.String("trace_id", req.TraceID),
			zap.String("record_id", req.RecordID),
			zap.Uint("ck_id", existingRecord.CKID))
		return existingRecord, nil
	}
	
	// 分布式锁保护CK选择和预占用过程 - 使用分片策略减少竞争
	lockKey := m.buildShardedLockKey(req.MerchantID, req.DepartmentID, req.TraceID)
	lockValue := fmt.Sprintf("%s:%d", req.TraceID, time.Now().UnixNano())

	// 使用重试机制获取分布式锁
	acquired, err := m.acquireDistributedLockWithRetry(ctx, lockKey, lockValue, m.lockTimeout)
	if err != nil {
		return nil, fmt.Errorf("获取分布式锁失败: %w", err)
	}
	if !acquired {
		return nil, fmt.Errorf("获取分布式锁超时，系统繁忙")
	}
	
	defer func() {
		if err := m.releaseDistributedLock(ctx, lockKey, lockValue); err != nil {
			m.logger.Error("释放分布式锁失败", zap.Error(err))
		}
	}()
	
	// 再次检查幂等性（双重检查）
	existingRecord, err = m.getPreoccupationRecord(ctx, idempotencyKey)
	if err == nil && existingRecord != nil {
		return existingRecord, nil
	}
	
	// 选择可用CK
	ck, err := m.selectAvailableCK(ctx, req.MerchantID, req.DepartmentID)
	if err != nil {
		return nil, fmt.Errorf("选择可用CK失败: %w", err)
	}
	
	// 创建预占用记录
	record := &CKPreoccupationRecord{
		CKID:         ck.ID,
		MerchantID:   req.MerchantID,
		DepartmentID: req.DepartmentID,
		TraceID:      req.TraceID,
		RecordID:     req.RecordID,
		Amount:       req.Amount,
		CardNumber:   req.CardNumber,
		Status:       "PREOCCUPIED",
		CreatedAt:    time.Now(),
		ExpiredAt:    time.Now().Add(m.preoccupationTimeout),
	}
	
	// 在事务中执行预占用
	err = m.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 1. 增加CK的绑卡计数
		result := tx.Model(&model.WalmartCK{}).
			Where("id = ? AND active = ? AND is_deleted = ? AND bind_count < total_limit", 
				ck.ID, true, false).
			Update("bind_count", gorm.Expr("bind_count + 1"))
		
		if result.Error != nil {
			return fmt.Errorf("更新CK绑卡计数失败: %w", result.Error)
		}
		
		if result.RowsAffected == 0 {
			return fmt.Errorf("CK %d 不可用或已达到限制", ck.ID)
		}
		
		// 2. 保存预占用记录到Redis
		recordJSON, err := json.Marshal(record)
		if err != nil {
			return fmt.Errorf("序列化预占用记录失败: %w", err)
		}
		
		err = m.redis.Set(ctx, idempotencyKey, recordJSON, m.preoccupationTimeout).Err()
		if err != nil {
			return fmt.Errorf("保存预占用记录失败: %w", err)
		}
		
		// 3. 添加到过期清理队列
		expireKey := fmt.Sprintf("expire:preoccupy:%d", record.ExpiredAt.Unix())
		err = m.redis.SAdd(ctx, expireKey, idempotencyKey).Err()
		if err != nil {
			m.logger.Error("添加到过期清理队列失败", zap.Error(err))
		}
		
		return nil
	})
	
	if err != nil {
		return nil, fmt.Errorf("预占用CK失败: %w", err)
	}
	
	m.logger.Info("CK预占用成功",
		zap.String("trace_id", req.TraceID),
		zap.String("record_id", req.RecordID),
		zap.Uint("ck_id", ck.ID),
		zap.Uint("department_id", req.DepartmentID),
		zap.Int("amount", req.Amount))
	
	return record, nil
}

// CommitPreoccupation 确认预占用（绑卡成功）
func (m *CKPreoccupationManager) CommitPreoccupation(ctx context.Context, traceID, recordID string, success bool, actualAmount int) error {
	idempotencyKey := fmt.Sprintf("preoccupy:%s:%s", traceID, recordID)
	
	// 获取预占用记录
	record, err := m.getPreoccupationRecord(ctx, idempotencyKey)
	if err != nil {
		return fmt.Errorf("获取预占用记录失败: %w", err)
	}
	if record == nil {
		return fmt.Errorf("预占用记录不存在: %s:%s", traceID, recordID)
	}
	
	// 分布式锁保护提交过程
	lockKey := fmt.Sprintf("lock:ck_commit:%d", record.CKID)
	lockValue := fmt.Sprintf("%s:%d", traceID, time.Now().UnixNano())
	
	acquired, err := m.acquireDistributedLock(ctx, lockKey, lockValue, m.lockTimeout)
	if err != nil {
		return fmt.Errorf("获取提交锁失败: %w", err)
	}
	if !acquired {
		return fmt.Errorf("获取提交锁超时")
	}
	
	defer func() {
		if err := m.releaseDistributedLock(ctx, lockKey, lockValue); err != nil {
			m.logger.Error("释放提交锁失败", zap.Error(err))
		}
	}()
	
	// 在事务中执行提交
	err = m.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if success {
			// 绑卡成功：更新最后绑卡时间和金额
			now := time.Now()
			updates := map[string]interface{}{
				"last_bind_time": &now,
			}
			
			// 检查是否达到限制，自动禁用
			var ck model.WalmartCK
			if err := tx.First(&ck, record.CKID).Error; err != nil {
				return fmt.Errorf("查询CK失败: %w", err)
			}
			
			if ck.BindCount >= ck.TotalLimit {
				updates["active"] = false
				m.logger.Info("CK达到限制，自动禁用",
					zap.Uint("ck_id", record.CKID),
					zap.Int("bind_count", ck.BindCount),
					zap.Int("total_limit", ck.TotalLimit))
			}
			
			result := tx.Model(&model.WalmartCK{}).Where("id = ?", record.CKID).Updates(updates)
			if result.Error != nil {
				return fmt.Errorf("更新CK状态失败: %w", result.Error)
			}
			
			// 记录绑卡成功的金额（用于数据一致性检查）
			record.Status = "COMMITTED"
			record.Amount = actualAmount
		} else {
			// 绑卡失败：回滚预占用的计数
			result := tx.Model(&model.WalmartCK{}).
				Where("id = ? AND bind_count > 0", record.CKID).
				Update("bind_count", gorm.Expr("bind_count - 1"))
			
			if result.Error != nil {
				return fmt.Errorf("回滚CK计数失败: %w", result.Error)
			}
			
			record.Status = "ROLLBACK"
		}
		
		// 更新预占用记录状态
		recordJSON, err := json.Marshal(record)
		if err != nil {
			return fmt.Errorf("序列化记录失败: %w", err)
		}
		
		// 保存最终状态（延长过期时间用于审计）
		err = m.redis.Set(ctx, idempotencyKey, recordJSON, 24*time.Hour).Err()
		if err != nil {
			return fmt.Errorf("更新记录状态失败: %w", err)
		}
		
		return nil
	})
	
	if err != nil {
		return fmt.Errorf("提交预占用失败: %w", err)
	}
	
	m.logger.Info("预占用提交成功",
		zap.String("trace_id", traceID),
		zap.String("record_id", recordID),
		zap.Uint("ck_id", record.CKID),
		zap.Bool("success", success),
		zap.Int("actual_amount", actualAmount))
	
	return nil
}

// selectAvailableCK 选择可用CK
func (m *CKPreoccupationManager) selectAvailableCK(ctx context.Context, merchantID, departmentID uint) (*model.WalmartCK, error) {
	var ck model.WalmartCK
	err := m.db.WithContext(ctx).Where(
		"merchant_id = ? AND department_id = ? AND active = ? AND is_deleted = ? AND bind_count < total_limit",
		merchantID, departmentID, true, false,
	).Order("bind_count ASC, last_bind_time ASC").First(&ck).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("部门 %d 没有可用的CK", departmentID)
		}
		return nil, fmt.Errorf("查询CK失败: %w", err)
	}
	
	return &ck, nil
}

// getPreoccupationRecord 获取预占用记录
func (m *CKPreoccupationManager) getPreoccupationRecord(ctx context.Context, key string) (*CKPreoccupationRecord, error) {
	data, err := m.redis.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, err
	}
	
	var record CKPreoccupationRecord
	err = json.Unmarshal([]byte(data), &record)
	if err != nil {
		return nil, err
	}
	
	return &record, nil
}

// acquireDistributedLock 获取分布式锁
func (m *CKPreoccupationManager) acquireDistributedLock(ctx context.Context, key, value string, timeout time.Duration) (bool, error) {
	result, err := m.redis.SetNX(ctx, key, value, timeout).Result()
	return result, err
}

// acquireDistributedLockWithRetry 带重试机制的分布式锁获取
func (m *CKPreoccupationManager) acquireDistributedLockWithRetry(ctx context.Context, key, value string, timeout time.Duration) (bool, error) {
	maxRetries := 5
	baseDelay := 200 * time.Millisecond
	backoffMultiplier := 1.5

	for attempt := 0; attempt < maxRetries; attempt++ {
		// 尝试获取锁
		acquired, err := m.acquireDistributedLock(ctx, key, value, timeout)
		if err != nil {
			m.logger.Warn("获取分布式锁时发生错误",
				zap.String("key", key),
				zap.Int("attempt", attempt+1),
				zap.Error(err))

			// 如果是Redis连接错误，等待后重试
			if attempt < maxRetries-1 {
				delay := time.Duration(float64(baseDelay) * math.Pow(backoffMultiplier, float64(attempt)))
				time.Sleep(delay)
				continue
			}
			return false, err
		}

		if acquired {
			if attempt > 0 {
				m.logger.Info("分布式锁获取成功",
					zap.String("key", key),
					zap.Int("attempts", attempt+1))
			}
			return true, nil
		}

		// 锁被占用，等待后重试
		if attempt < maxRetries-1 {
			delay := time.Duration(float64(baseDelay) * math.Pow(backoffMultiplier, float64(attempt)))
			m.logger.Debug("分布式锁被占用，等待重试",
				zap.String("key", key),
				zap.Int("attempt", attempt+1),
				zap.Duration("delay", delay))
			time.Sleep(delay)
		}
	}

	m.logger.Warn("分布式锁获取失败，已达到最大重试次数",
		zap.String("key", key),
		zap.Int("max_retries", maxRetries))

	return false, nil
}

// releaseDistributedLock 释放分布式锁
func (m *CKPreoccupationManager) releaseDistributedLock(ctx context.Context, key, value string) error {
	script := `
		if redis.call("get", KEYS[1]) == ARGV[1] then
			return redis.call("del", KEYS[1])
		else
			return 0
		end
	`
	return m.redis.Eval(ctx, script, []string{key}, value).Err()
}

// startCleanupWorker 启动清理过期预占用的后台任务
func (m *CKPreoccupationManager) startCleanupWorker() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()
	
	for range ticker.C {
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		
		// 清理过期的预占用
		currentTime := time.Now().Unix()
		expireKey := fmt.Sprintf("expire:preoccupy:%d", currentTime)
		
		expiredKeys, err := m.redis.SMembers(ctx, expireKey).Result()
		if err != nil {
			m.logger.Error("获取过期预占用列表失败", zap.Error(err))
			cancel()
			continue
		}
		
		for _, key := range expiredKeys {
			if err := m.cleanupExpiredPreoccupation(ctx, key); err != nil {
				m.logger.Error("清理过期预占用失败",
					zap.String("key", key),
					zap.Error(err))
			}
		}
		
		// 删除过期键集合
		m.redis.Del(ctx, expireKey)
		cancel()
	}
}

// cleanupExpiredPreoccupation 清理过期的预占用
func (m *CKPreoccupationManager) cleanupExpiredPreoccupation(ctx context.Context, key string) error {
	record, err := m.getPreoccupationRecord(ctx, key)
	if err != nil || record == nil {
		return nil
	}
	
	// 只清理仍处于预占用状态的记录
	if record.Status == "PREOCCUPIED" {
		// 回滚CK计数
		result := m.db.WithContext(ctx).Model(&model.WalmartCK{}).
			Where("id = ? AND bind_count > 0", record.CKID).
			Update("bind_count", gorm.Expr("bind_count - 1"))
		
		if result.Error != nil {
			return fmt.Errorf("回滚过期预占用失败: %w", result.Error)
		}
		
		// 更新记录状态
		record.Status = "EXPIRED"
		recordJSON, _ := json.Marshal(record)
		m.redis.Set(ctx, key, recordJSON, 24*time.Hour)
		
		m.logger.Info("清理过期预占用",
			zap.String("trace_id", record.TraceID),
			zap.String("record_id", record.RecordID),
			zap.Uint("ck_id", record.CKID))
	}
	
	return nil
}

// buildShardedLockKey 构建分片锁键，减少锁竞争
func (m *CKPreoccupationManager) buildShardedLockKey(merchantID, departmentID uint, traceID string) string {
	// 使用traceID的哈希值进行分片，减少同一商户下的锁竞争
	hash := 0
	for _, c := range traceID {
		hash = hash*31 + int(c)
	}
	if hash < 0 {
		hash = -hash
	}

	// 分成16个分片，减少锁竞争
	shard := hash % 16

	return fmt.Sprintf("lock:ck_preoccupy:%d:%d:shard_%d", merchantID, departmentID, shard)
}

// CKPreoccupationRequest 预占用请求
type CKPreoccupationRequest struct {
	MerchantID   uint   `json:"merchant_id"`
	DepartmentID uint   `json:"department_id"`
	TraceID      string `json:"trace_id"`
	RecordID     string `json:"record_id"`
	Amount       int    `json:"amount"`
	CardNumber   string `json:"card_number"`
}
