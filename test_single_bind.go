package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"walmart-bind-card-processor/internal/config"
	"walmart-bind-card-processor/internal/model"
	"walmart-bind-card-processor/internal/services"

	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func main() {
	fmt.Println("🧪 测试单个绑卡请求（验证部门ID修复）...")

	// 加载配置
	cfg, err := config.LoadConfig("config.yaml")
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 构建数据库连接字符串
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		cfg.Database.User,
		cfg.Database.Password,
		cfg.Database.Host,
		cfg.Database.Port,
		cfg.Database.DBName)

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}

	// 连接Redis
	rdb := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", cfg.Redis.Host, cfg.Redis.Port),
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
	})

	// 创建日志器
	logger, _ := zap.NewDevelopment()

	// 创建绑卡处理器
	processor, err := services.NewBindCardProcessor(db, rdb, logger, cfg, nil)
	if err != nil {
		log.Fatalf("创建绑卡处理器失败: %v", err)
	}

	// 创建测试绑卡记录
	testRecordID := uuid.New().String()
	testMerchantID := uint(4) // 使用商户ID 4
	testTraceID := fmt.Sprintf("TEST_DEPT_FIX_%d", time.Now().Unix())

	// 创建测试卡记录
	requestData := "{}"
	cardRecord := &model.CardRecord{
		ID:           testRecordID,
		CardNumber:   "2326123456783312",
		CardPassword: stringPtr("test123"),
		TraceID:      stringPtr(testTraceID),
		MerchantID:   int64(testMerchantID),
		Status:       "processing",
		Amount:       100,
		RequestData:  requestData,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if err := db.Create(cardRecord).Error; err != nil {
		log.Fatalf("创建测试卡记录失败: %v", err)
	}

	fmt.Printf("✅ 创建测试卡记录: %s\n", testRecordID)

	// 创建测试消息
	msg := &services.BindCardMessage{
		RecordID:     testRecordID,
		CardNumber:   "2326123456783312",
		CardPassword: "test123",
		TraceID:      testTraceID,
		MerchantID:   testMerchantID,
		Amount:       100,
		DepartmentID: nil, // 让系统自动选择部门
	}

	// 测试部门选择和日志记录
	ctx := context.Background()

	fmt.Println("🔄 开始测试绑卡处理...")

	// 处理绑卡（这会触发部门选择和日志记录）
	err = processor.ProcessBindCard(ctx, msg)
	if err != nil {
		fmt.Printf("⚠️  绑卡处理结果: %v\n", err)
	} else {
		fmt.Println("✅ 绑卡处理完成")
	}

	// 等待一秒确保日志写入
	time.Sleep(2 * time.Second)

	// 查询绑卡日志，检查department_id是否正确记录
	var logs []model.BindingLog
	err = db.Where("card_record_id = ?", testRecordID).
		Order("created_at ASC").
		Find(&logs).Error

	if err != nil {
		log.Fatalf("查询绑卡日志失败: %v", err)
	}

	fmt.Printf("\n📊 找到 %d 条绑卡日志:\n", len(logs))
	fmt.Println("================================================================================")

	departmentIDFound := false
	for i, logEntry := range logs {
		fmt.Printf("日志 #%d:\n", i+1)
		fmt.Printf("  ID: %s\n", logEntry.ID)
		fmt.Printf("  日志类型: %s\n", logEntry.LogType)
		fmt.Printf("  消息: %s\n", logEntry.Message)
		
		if logEntry.MerchantID != nil {
			fmt.Printf("  商户ID: %d\n", *logEntry.MerchantID)
		} else {
			fmt.Printf("  商户ID: <空>\n")
		}
		
		if logEntry.DepartmentID != nil {
			fmt.Printf("  ✅ 部门ID: %d\n", *logEntry.DepartmentID)
			departmentIDFound = true
		} else {
			fmt.Printf("  ❌ 部门ID: <空>\n")
		}
		
		if logEntry.WalmartCKID != nil {
			fmt.Printf("  CK_ID: %d\n", *logEntry.WalmartCKID)
		}
		
		// 解析详细信息
		if logEntry.Details != nil {
			var details map[string]interface{}
			if err := json.Unmarshal([]byte(*logEntry.Details), &details); err == nil {
				if deptID, exists := details["department_id"]; exists {
					fmt.Printf("  详细信息中的部门ID: %v\n", deptID)
				}
			}
		}
		
		fmt.Printf("  创建时间: %s\n", logEntry.CreatedAt.Format("2006-01-02 15:04:05"))
		fmt.Println("  ------------------------------------------------------------")
	}

	// 总结测试结果
	fmt.Println("\n🎯 测试结果总结:")
	if departmentIDFound {
		fmt.Println("✅ 成功！部门ID已正确记录到绑卡日志中")
	} else {
		fmt.Println("❌ 失败！部门ID仍然为空")
	}

	// 清理测试数据
	fmt.Println("\n🧹 清理测试数据...")
	db.Where("card_record_id = ?", testRecordID).Delete(&model.BindingLog{})
	db.Where("id = ?", testRecordID).Delete(&model.CardRecord{})
	fmt.Println("✅ 测试数据清理完成")
}

func stringPtr(s string) *string {
	return &s
}
