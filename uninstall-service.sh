#!/bin/bash

# Walmart Card Binding Server - Service Uninstallation Script
# 沃尔玛绑卡服务卸载脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SERVICE_NAME="walmart-bind-card"
SERVICE_USER="walmart"
SERVICE_GROUP="walmart"
INSTALL_DIR="/opt/walmart-bind-card-server"
LOG_DIR="/var/log/walmart-bind-card"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 确认卸载
confirm_uninstall() {
    echo ""
    log_warn "⚠️  警告：即将卸载 Walmart Card Binding Server 服务"
    echo ""
    echo "这将会："
    echo "1. 停止并禁用服务"
    echo "2. 删除systemd服务文件"
    echo "3. 删除管理脚本"
    echo "4. 删除日志轮转配置"
    echo "5. 可选择删除用户、目录和日志文件"
    echo ""
    
    read -p "确定要继续吗？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "取消卸载"
        exit 0
    fi
}

# 停止并禁用服务
stop_and_disable_service() {
    log_step "停止并禁用服务..."
    
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        systemctl stop "$SERVICE_NAME"
        log_info "服务已停止"
    else
        log_info "服务未运行"
    fi
    
    if systemctl is-enabled --quiet "$SERVICE_NAME"; then
        systemctl disable "$SERVICE_NAME"
        log_info "服务已禁用开机自启"
    else
        log_info "服务未启用开机自启"
    fi
}

# 删除systemd服务文件
remove_systemd_service() {
    log_step "删除systemd服务文件..."
    
    if [[ -f "/etc/systemd/system/$SERVICE_NAME.service" ]]; then
        rm -f "/etc/systemd/system/$SERVICE_NAME.service"
        systemctl daemon-reload
        log_info "systemd服务文件已删除"
    else
        log_info "systemd服务文件不存在"
    fi
}

# 删除管理脚本
remove_management_scripts() {
    log_step "删除管理脚本..."
    
    if [[ -f "/usr/local/bin/walmart-service" ]]; then
        rm -f "/usr/local/bin/walmart-service"
        log_info "管理脚本已删除"
    else
        log_info "管理脚本不存在"
    fi
}

# 删除日志轮转配置
remove_logrotate_config() {
    log_step "删除日志轮转配置..."
    
    if [[ -f "/etc/logrotate.d/walmart-bind-card" ]]; then
        rm -f "/etc/logrotate.d/walmart-bind-card"
        log_info "日志轮转配置已删除"
    else
        log_info "日志轮转配置不存在"
    fi
}

# 询问是否删除用户和目录
ask_remove_user_and_dirs() {
    echo ""
    log_warn "是否删除服务用户、安装目录和日志文件？"
    echo ""
    echo "这将删除："
    echo "- 用户: $SERVICE_USER"
    echo "- 组: $SERVICE_GROUP"
    echo "- 安装目录: $INSTALL_DIR"
    echo "- 日志目录: $LOG_DIR"
    echo ""
    log_warn "⚠️  注意：这将永久删除所有数据和日志文件！"
    echo ""
    
    read -p "确定要删除吗？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        remove_user_and_dirs
    else
        log_info "保留用户、目录和日志文件"
    fi
}

# 删除用户和目录
remove_user_and_dirs() {
    log_step "删除用户、目录和日志文件..."
    
    # 删除目录
    if [[ -d "$INSTALL_DIR" ]]; then
        rm -rf "$INSTALL_DIR"
        log_info "安装目录已删除: $INSTALL_DIR"
    fi
    
    if [[ -d "$LOG_DIR" ]]; then
        rm -rf "$LOG_DIR"
        log_info "日志目录已删除: $LOG_DIR"
    fi
    
    # 删除用户
    if getent passwd "$SERVICE_USER" > /dev/null 2>&1; then
        userdel "$SERVICE_USER"
        log_info "用户已删除: $SERVICE_USER"
    fi
    
    # 删除组
    if getent group "$SERVICE_GROUP" > /dev/null 2>&1; then
        groupdel "$SERVICE_GROUP"
        log_info "组已删除: $SERVICE_GROUP"
    fi
}

# 显示卸载完成信息
show_uninstall_complete() {
    log_step "卸载完成！"
    
    echo ""
    echo "=========================================="
    echo "  Walmart Card Binding Server 卸载完成"
    echo "=========================================="
    echo "已完成的操作："
    echo "✅ 停止并禁用服务"
    echo "✅ 删除systemd服务文件"
    echo "✅ 删除管理脚本"
    echo "✅ 删除日志轮转配置"
    echo ""
    echo "如果您选择了删除用户和目录："
    echo "✅ 删除服务用户和组"
    echo "✅ 删除安装目录和日志文件"
    echo ""
    echo "服务已完全卸载！"
    echo "=========================================="
}

# 主函数
main() {
    log_info "开始卸载 Walmart Card Binding Server 服务..."
    
    check_root
    confirm_uninstall
    stop_and_disable_service
    remove_systemd_service
    remove_management_scripts
    remove_logrotate_config
    ask_remove_user_and_dirs
    show_uninstall_complete
}

# 执行主函数
main "$@"
