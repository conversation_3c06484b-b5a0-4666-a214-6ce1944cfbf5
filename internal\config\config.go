package config

import (
	"fmt"
	"time"

	"github.com/spf13/viper"
)

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Type            string        `mapstructure:"type"`
	Host            string        `mapstructure:"host"`
	Port            int           `mapstructure:"port"`
	User            string        `mapstructure:"user"`
	Password        string        `mapstructure:"password"`
	DBName          string        `mapstructure:"db_name"`
	Charset         string        `mapstructure:"charset"`
	MaxOpenConns    int           `mapstructure:"max_open_conns"`
	MaxIdleConns    int           `mapstructure:"max_idle_conns"`
	ConnMaxLifetime time.Duration `mapstructure:"conn_max_lifetime"`
	ConnMaxIdleTime time.Duration `mapstructure:"conn_max_idle_time"`
	QueryTimeout    time.Duration `mapstructure:"query_timeout"`
	ExecTimeout     time.Duration `mapstructure:"exec_timeout"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host                string        `mapstructure:"host"`
	Port                int           `mapstructure:"port"`
	Password            string        `mapstructure:"password"`
	DB                  int           `mapstructure:"db"`
	PoolSize            int           `mapstructure:"pool_size"`
	MinIdleConns        int           `mapstructure:"min_idle_conns"`
	MaxRetries          int           `mapstructure:"max_retries"`
	DialTimeout         time.Duration `mapstructure:"dial_timeout"`
	ReadTimeout         time.Duration `mapstructure:"read_timeout"`
	WriteTimeout        time.Duration `mapstructure:"write_timeout"`
	PoolTimeout         time.Duration `mapstructure:"pool_timeout"`
	IdleTimeout         time.Duration `mapstructure:"idle_timeout"`
	IdleCheckFrequency  time.Duration `mapstructure:"idle_check_frequency"`
	PipelineSize        int           `mapstructure:"pipeline_size"`
}// RabbitMQConfig RabbitMQ配置
type RabbitMQConfig struct {
	URL                   string            `mapstructure:"url"`
	Host                  string            `mapstructure:"host"`
	Port                  int               `mapstructure:"port"`
	User                  string            `mapstructure:"user"`
	Password              string            `mapstructure:"password"`
	VHost                 string            `mapstructure:"vhost"`
	ConsumerPrefetchCount int               `mapstructure:"consumer_prefetch_count"`
	ConsumerConcurrency   int               `mapstructure:"consumer_concurrency"`
	MaxRetries            int               `mapstructure:"max_retries"`
	RetryDelay            time.Duration     `mapstructure:"retry_delay"`
	QueueBindCard         string            `mapstructure:"queue_bind_card"`
	QueueCallback         string            `mapstructure:"queue_callback"`
	Queues                map[string]string `mapstructure:"queues"`

	// 重连配置
	ReconnectMaxRetries   int           `mapstructure:"reconnect_max_retries"`   // 最大重连次数
	ReconnectInitialDelay time.Duration `mapstructure:"reconnect_initial_delay"` // 初始重连延迟
	ReconnectMaxDelay     time.Duration `mapstructure:"reconnect_max_delay"`     // 最大重连延迟
	ReconnectMultiplier   float64       `mapstructure:"reconnect_multiplier"`    // 重连延迟倍数

	// 队列管理配置
	QueueManagement QueueManagementConfig `mapstructure:"queue_management"`
}

// QueueManagementConfig 队列管理配置
type QueueManagementConfig struct {
	// 是否在启动时检查队列存在性
	CheckQueueExistence bool `mapstructure:"check_queue_existence"`
	// 是否允许自动创建队列
	AutoCreateQueues bool `mapstructure:"auto_create_queues"`
	// 是否启用严格模式（禁止创建任何临时队列）
	StrictMode bool `mapstructure:"strict_mode"`
	// 队列声明超时时间
	QueueDeclareTimeout time.Duration `mapstructure:"queue_declare_timeout"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level  string            `mapstructure:"level"`
	Format string            `mapstructure:"format"`
	Output string            `mapstructure:"output"`
	Fields map[string]string `mapstructure:"fields"`
}

// BindCardRetryConfig 绑卡重试配置
type BindCardRetryConfig struct {
	MaxAttempts        int           `mapstructure:"max_attempts"`
	InitialDelay       time.Duration `mapstructure:"initial_delay"`
	MaxDelay           time.Duration `mapstructure:"max_delay"`
	BackoffMultiplier  float64       `mapstructure:"backoff_multiplier"`
	RetryableErrors    []string      `mapstructure:"retryable_errors"`
	CKSwitchErrors     []string      `mapstructure:"ck_switch_errors"`
	NonRetryableErrors []string      `mapstructure:"non_retryable_errors"`
}

// BalanceQueryRetryConfig 金额查询重试配置
type BalanceQueryRetryConfig struct {
	MaxAttempts        int           `mapstructure:"max_attempts"`
	InitialDelay       time.Duration `mapstructure:"initial_delay"`
	MaxDelay           time.Duration `mapstructure:"max_delay"`
	BackoffMultiplier  float64       `mapstructure:"backoff_multiplier"`
	RetryableErrors    []string      `mapstructure:"retryable_errors"`
	NonRetryableErrors []string      `mapstructure:"non_retryable_errors"`
}

// CallbackRetryConfig 回调重试配置
type CallbackRetryConfig struct {
	MaxAttempts       int           `mapstructure:"max_attempts"`
	InitialDelay      time.Duration `mapstructure:"initial_delay"`
	MaxDelay          time.Duration `mapstructure:"max_delay"`
	BackoffMultiplier float64       `mapstructure:"backoff_multiplier"`
	Timeout           time.Duration `mapstructure:"timeout"`
}

// RetryStrategyConfig 重试策略配置
type RetryStrategyConfig struct {
	BindCard     BindCardRetryConfig     `mapstructure:"bind_card"`
	BalanceQuery BalanceQueryRetryConfig `mapstructure:"balance_query"`
	Callback     CallbackRetryConfig     `mapstructure:"callback"`
}

// FeaturesConfig 功能开关配置
type FeaturesConfig struct {
	BindCard BindCardFeatureConfig `mapstructure:"bind_card"`
}

// BindCardFeatureConfig 绑卡功能配置
type BindCardFeatureConfig struct {
	Enabled bool `mapstructure:"enabled"`
}

// WalmartAPIClientConfig 沃尔玛API客户端配置
type WalmartAPIClientConfig struct {
	BaseURL        string `mapstructure:"base_url"`
	TimeoutSeconds int    `mapstructure:"timeout_seconds"`
	// 注意：调试模式从请求上下文动态判断，不在配置中定义
}

// WalmartConfig 沃尔玛配置
type WalmartConfig struct {
	APIClient  WalmartAPIClientConfig `mapstructure:"api_client"`
	Monitoring MonitoringConfig       `mapstructure:"monitoring"`
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	EnableMetrics         bool `mapstructure:"enable_metrics"`
	EnableDetailedLogging bool `mapstructure:"enable_detailed_logging"`
	SlowRequestThreshold  int  `mapstructure:"slow_request_threshold"`
}

// CKManagementConfig CK管理配置
type CKManagementConfig struct {
	DistributedLock DistributedLockConfig `mapstructure:"distributed_lock"`
	StatusSync      StatusSyncConfig      `mapstructure:"status_sync"`
	Monitoring      CKMonitoringConfig    `mapstructure:"monitoring"`
	WeightAlgorithm WeightAlgorithmConfig `mapstructure:"weight_algorithm"`
	Preoccupation   PreoccupationConfig   `mapstructure:"preoccupation"`
}

// DistributedLockConfig 分布式锁配置
type DistributedLockConfig struct {
	Timeout       time.Duration `mapstructure:"timeout"`
	RetryInterval time.Duration `mapstructure:"retry_interval"`
	MaxRetries    int           `mapstructure:"max_retries"`
}

// StatusSyncConfig CK状态同步配置
type StatusSyncConfig struct {
	CacheExpiry      time.Duration `mapstructure:"cache_expiry"`
	SyncInterval     time.Duration `mapstructure:"sync_interval"`
	FailureThreshold int           `mapstructure:"failure_threshold"`
	RecoveryInterval time.Duration `mapstructure:"recovery_interval"`
	EventBufferSize  int           `mapstructure:"event_buffer_size"`
}

// CKMonitoringConfig CK监控配置
type CKMonitoringConfig struct {
	MetricsRetention time.Duration         `mapstructure:"metrics_retention"`
	LogRetention     time.Duration         `mapstructure:"log_retention"`
	BatchSize        int                   `mapstructure:"batch_size"`
	FlushInterval    time.Duration         `mapstructure:"flush_interval"`
	AlertThresholds  AlertThresholdsConfig `mapstructure:"alert_thresholds"`
}

// AlertThresholdsConfig 告警阈值配置
type AlertThresholdsConfig struct {
	SuccessRate         float64       `mapstructure:"success_rate"`
	ResponseTime        time.Duration `mapstructure:"response_time"`
	ConsecutiveFailures int           `mapstructure:"consecutive_failures"`
	ErrorRate           float64       `mapstructure:"error_rate"`
}

// WeightAlgorithmConfig 权重算法配置
type WeightAlgorithmConfig struct {
	CacheRefreshInterval    time.Duration `mapstructure:"cache_refresh_interval"`
	LockTimeout            time.Duration `mapstructure:"lock_timeout"`
	MaxRetries             int           `mapstructure:"max_retries"`
	CircuitBreakerThreshold int           `mapstructure:"circuit_breaker_threshold"`
}

// PreoccupationConfig CK预占用配置
type PreoccupationConfig struct {
	Timeout         time.Duration `mapstructure:"timeout"`
	CleanupInterval time.Duration `mapstructure:"cleanup_interval"`
	MaxConcurrent   int           `mapstructure:"max_concurrent"`
}

// GetDSN 获取数据库连接字符串
func (c *DatabaseConfig) GetDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
		c.User, c.Password, c.Host, c.Port, c.DBName, c.Charset)
}

// GetRedisAddr 获取Redis地址
func (c *RedisConfig) GetRedisAddr() string {
	return fmt.Sprintf("%s:%d", c.Host, c.Port)
}

// GetRabbitMQURL 获取RabbitMQ连接URL
func (r *RabbitMQConfig) GetRabbitMQURL() string {
	if r.URL != "" {
		return r.URL
	}
	
	if r.Host == "" {
		r.Host = "localhost"
	}
	if r.Port == 0 {
		r.Port = 5672
	}
	if r.User == "" {
		r.User = "guest"
	}
	if r.Password == "" {
		r.Password = "guest"
	}
	if r.VHost == "" {
		r.VHost = "/"
	}
	
	return fmt.Sprintf("amqp://%s:%s@%s:%d/%s", r.User, r.Password, r.Host, r.Port, r.VHost)
}

// SetReconnectDefaults 设置重连配置的默认值
func (r *RabbitMQConfig) SetReconnectDefaults() {
	if r.ReconnectMaxRetries <= 0 {
		r.ReconnectMaxRetries = 5
	}
	if r.ReconnectInitialDelay <= 0 {
		r.ReconnectInitialDelay = 2 * time.Second
	}
	if r.ReconnectMaxDelay <= 0 {
		r.ReconnectMaxDelay = 30 * time.Second
	}
	if r.ReconnectMultiplier <= 0 {
		r.ReconnectMultiplier = 1.5
	}
}

// ProcessorConfig 处理器配置
type ProcessorConfig struct {
	EnableHealthCheck bool `mapstructure:"enable_health_check"`
}

// Config 应用配置结构
type Config struct {
	Server        ServerConfig        `mapstructure:"server"`
	Database      DatabaseConfig      `mapstructure:"database"`
	Redis         RedisConfig         `mapstructure:"redis"`
	RabbitMQ      RabbitMQConfig      `mapstructure:"rabbitmq"`
	Logging       LoggingConfig       `mapstructure:"logging"`
	RetryStrategy RetryStrategyConfig `mapstructure:"retry_strategy"`
	Processor     ProcessorConfig     `mapstructure:"processor"`
	Features      FeaturesConfig      `mapstructure:"features"`
	Walmart       WalmartConfig       `mapstructure:"walmart"`
	CKManagement  CKManagementConfig  `mapstructure:"ck_management"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port int    `mapstructure:"port"`
	Mode string `mapstructure:"mode"`
	Host string `mapstructure:"host"`
}

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	// 设置配置文件路径和名称
	viper.SetConfigFile(configPath)
	viper.SetConfigType("yaml")

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	// 创建配置对象
	var config Config

	// 解析配置到结构体
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// 设置默认值（如果配置文件中没有指定）
	setDefaults(&config)

	return &config, nil
}

// setDefaults 设置默认配置值
func setDefaults(config *Config) {
	// 服务器默认配置
	if config.Server.Port == 0 {
		config.Server.Port = 21000
	}
	if config.Server.Host == "" {
		config.Server.Host = "0.0.0.0"
	}
	if config.Server.Mode == "" {
		config.Server.Mode = "debug"
	}

	// 数据库默认配置
	if config.Database.Type == "" {
		config.Database.Type = "mysql"
	}
	if config.Database.Charset == "" {
		config.Database.Charset = "utf8mb4"
	}
	if config.Database.MaxOpenConns == 0 {
		config.Database.MaxOpenConns = 20
	}
	if config.Database.MaxIdleConns == 0 {
		config.Database.MaxIdleConns = 10
	}

	// Redis默认配置
	if config.Redis.Host == "" {
		config.Redis.Host = "localhost"
	}
	if config.Redis.Port == 0 {
		config.Redis.Port = 6379
	}

	// 日志默认配置
	if config.Logging.Level == "" {
		config.Logging.Level = "info"
	}
	if config.Logging.Format == "" {
		config.Logging.Format = "json"
	}
	if config.Logging.Output == "" {
		config.Logging.Output = "stdout"
	}

	// 保留原有的默认配置作为后备
	if config.RabbitMQ.Host == "" {
		config.RabbitMQ = RabbitMQConfig{
			Host:                  "localhost",
			Port:                  5672,
			User:                  "walmart_card",
			Password:              "7c222fb2927d828af22f592134e8932480637c0d",
			VHost:                 "/walmart_card",
			ConsumerPrefetchCount: 10,
			ConsumerConcurrency:   5,
			QueueBindCard:         "bind_card_queue",
			QueueCallback:         "bind_card_callback_queue",
			ReconnectMaxRetries:   5,
			ReconnectInitialDelay: 2 * time.Second,
			ReconnectMaxDelay:     30 * time.Second,
			ReconnectMultiplier:   1.5,
			QueueManagement: QueueManagementConfig{
				CheckQueueExistence: true,
				AutoCreateQueues:    false, // 默认禁止自动创建队列
				StrictMode:          true,  // 默认启用严格模式
				QueueDeclareTimeout: 10 * time.Second,
			},
		}
	}

	// 单独设置队列名称默认值
	if config.RabbitMQ.QueueBindCard == "" {
		config.RabbitMQ.QueueBindCard = "bind_card_queue"
	}
	if config.RabbitMQ.QueueCallback == "" {
		config.RabbitMQ.QueueCallback = "bind_card_callback_queue"
	}

	// 设置队列管理默认配置
	if config.RabbitMQ.QueueManagement.QueueDeclareTimeout == 0 {
		config.RabbitMQ.QueueManagement = QueueManagementConfig{
			CheckQueueExistence: true,
			AutoCreateQueues:    false,
			StrictMode:          true,
			QueueDeclareTimeout: 10 * time.Second,
		}
	}

	// 重试策略默认配置
	if config.RetryStrategy.BindCard.MaxAttempts == 0 {
		config.RetryStrategy = RetryStrategyConfig{
			BindCard: BindCardRetryConfig{
				MaxAttempts:       3,
				InitialDelay:      5 * time.Second,
				MaxDelay:          60 * time.Second,
				BackoffMultiplier: 2.0,
				RetryableErrors: []string{
					"网络超时", "连接超时", "临时不可用", "请求超时",
					"timeout", "connection.*reset", "网络连接异常",
					"服务暂时不可用", "系统繁忙，请稍后再试",
				},
				CKSwitchErrors: []string{
					"请先去登录", "去登录", "您绑卡已超过单日20张限制",
					"错误次数过多,请稍后再试", "数据异常", "服务器繁忙",
					"账号异常", "操作频繁", "需要重新登录",
					"errorcode.*203", "errorcode.*110224", "errorcode.*110134",
					"errorcode.*110444", "errorcode.*200",
				},
				NonRetryableErrors: []string{
					"卡已被其他用户绑定", "余额不足", "卡片已过期",
					"密码错误", "卡号格式错误", "暂不支持该类型卡",
					"卡片无效", "请求过期", "需要隐式登录",
					"errorcode.*10131", "errorcode.*5042", "errorcode.*5041",
					"errorcode.*506", "errorcode.*110445", "errorcode.*9999",
					"绑卡金额与真实金额不符",
				},
			},
			BalanceQuery: BalanceQueryRetryConfig{
				MaxAttempts:       2,
				InitialDelay:      500 * time.Millisecond,
				MaxDelay:          5 * time.Second,
				BackoffMultiplier: 2.0,
				RetryableErrors: []string{
					"网络超时", "连接超时", "临时不可用", "timeout", "网络异常", "连接失败",
				},
				NonRetryableErrors: []string{
					"请先去登录", "登录状态失效", "用户未登录", "会话过期",
					"卡号不存在", "卡片无效", "权限不足", "账号异常", "数据异常",
					"errorcode.*203", "errorcode.*110224", "errorcode.*110134",
				},
			},
			Callback: CallbackRetryConfig{
				MaxAttempts:       3,
				InitialDelay:      2 * time.Second,
				MaxDelay:          30 * time.Second,
				BackoffMultiplier: 2.0,
				Timeout:           10 * time.Second,
			},
		}
	}

	// Features默认配置
	if !config.Features.BindCard.Enabled {
		config.Features = FeaturesConfig{
			BindCard: BindCardFeatureConfig{
				Enabled: true, // 默认启用绑卡功能
			},
		}
	}
}