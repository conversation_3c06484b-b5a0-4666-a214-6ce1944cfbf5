package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"walmart-bind-card-processor/internal/config"
	"walmart-bind-card-processor/internal/model"
	"walmart-bind-card-processor/internal/services"

	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func main() {
	fmt.Println("🧪 测试新的process_time修复...")

	// 加载配置
	cfg, err := config.LoadConfig("config.yaml")
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 构建数据库连接字符串
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		cfg.Database.User,
		cfg.Database.Password,
		cfg.Database.Host,
		cfg.Database.Port,
		cfg.Database.DBName)

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}

	// 连接Redis
	rdb := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", cfg.Redis.Host, cfg.Redis.Port),
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
	})

	// 创建日志器
	logger, _ := zap.NewDevelopment()

	// 创建绑卡处理器
	processor, err := services.NewBindCardProcessor(db, rdb, logger, cfg, nil)
	if err != nil {
		log.Fatalf("创建绑卡处理器失败: %v", err)
	}

	// 创建测试绑卡记录
	testRecordID := uuid.New().String()
	testMerchantID := uint(4) // 使用商户ID 4
	testTraceID := fmt.Sprintf("TEST_PROCESS_TIME_%d", time.Now().Unix())

	// 创建测试卡记录
	requestData := "{}"
	cardRecord := &model.CardRecord{
		ID:           testRecordID,
		CardNumber:   "2326123456783312",
		CardPassword: stringPtr("test123"),
		TraceID:      stringPtr(testTraceID),
		MerchantID:   int64(testMerchantID),
		Status:       "processing",
		Amount:       100,
		RequestData:  requestData,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if err := db.Create(cardRecord).Error; err != nil {
		log.Fatalf("创建测试卡记录失败: %v", err)
	}

	fmt.Printf("✅ 创建测试卡记录: %s\n", testRecordID)
	fmt.Printf("📅 记录创建时间: %s\n", cardRecord.CreatedAt.Format("2006-01-02 15:04:05.000"))

	// 创建测试消息
	msg := &services.BindCardMessage{
		RecordID:     testRecordID,
		CardNumber:   "2326123456783312",
		CardPassword: "test123",
		TraceID:      testTraceID,
		MerchantID:   testMerchantID,
		Amount:       100,
		DepartmentID: nil, // 让系统自动选择部门
		Debug:        true, // 使用调试模式避免真实API调用
	}

	// 测试绑卡处理
	ctx := context.Background()

	fmt.Println("🔄 开始测试绑卡处理...")
	startTime := time.Now()

	// 处理绑卡
	err = processor.ProcessBindCard(ctx, msg)
	if err != nil {
		fmt.Printf("⚠️  绑卡处理结果: %v\n", err)
	} else {
		fmt.Println("✅ 绑卡处理完成")
	}

	endTime := time.Now()
	actualProcessTime := endTime.Sub(startTime).Seconds()
	fmt.Printf("⏱️  实际处理耗时: %.3f秒\n", actualProcessTime)

	// 等待一秒确保数据库更新完成
	time.Sleep(2 * time.Second)

	// 查询更新后的记录
	var updatedRecord model.CardRecord
	err = db.Where("id = ?", testRecordID).First(&updatedRecord).Error
	if err != nil {
		log.Fatalf("查询更新后的记录失败: %v", err)
	}

	fmt.Println("\n📊 process_time修复效果分析:")
	fmt.Println("================================================================================")
	fmt.Printf("记录ID: %s\n", updatedRecord.ID)
	fmt.Printf("状态: %s\n", updatedRecord.Status)
	fmt.Printf("创建时间: %s\n", updatedRecord.CreatedAt.Format("2006-01-02 15:04:05.000"))
	fmt.Printf("更新时间: %s\n", updatedRecord.UpdatedAt.Format("2006-01-02 15:04:05.000"))

	// 计算基于数据库时间戳的实际处理时间
	dbActualTime := updatedRecord.UpdatedAt.Sub(updatedRecord.CreatedAt).Seconds()
	fmt.Printf("数据库时间戳计算的处理时间: %.3f秒\n", dbActualTime)

	if updatedRecord.ProcessTime != nil {
		recordedTime := *updatedRecord.ProcessTime
		fmt.Printf("记录的process_time: %.3f秒\n", recordedTime)

		// 计算差异
		diff := dbActualTime - recordedTime
		fmt.Printf("时间差异: %.3f秒\n", diff)

		if diff < 0.5 && diff > -0.5 {
			fmt.Println("✅ process_time修复成功！时间记录准确")
		} else {
			fmt.Printf("❌ process_time仍有问题，差异: %.3f秒\n", diff)
		}
	} else {
		fmt.Println("❌ process_time为空")
	}

	// 检查部门ID是否也被正确记录
	if updatedRecord.DepartmentID != nil {
		fmt.Printf("✅ 部门ID已记录: %d\n", *updatedRecord.DepartmentID)
	} else {
		fmt.Println("❌ 部门ID仍为空")
	}

	// 清理测试数据
	fmt.Println("\n🧹 清理测试数据...")
	db.Where("card_record_id = ?", testRecordID).Delete(&model.BindingLog{})
	db.Where("id = ?", testRecordID).Delete(&model.CardRecord{})
	fmt.Println("✅ 测试数据清理完成")
}

func stringPtr(s string) *string {
	return &s
}
