# Walmart绑卡处理器 - 多平台构建脚本
# 支持Linux、Windows、macOS的Go应用交叉编译

param(
    [string]$Platform = "all",           # all, linux, windows, darwin, linux-amd64, etc.
    [string]$Version = "",               # 版本号，留空则自动检测
    [switch]$Clean = $true,             # 清理构建产物
    [switch]$Obfuscate = $true,         # 代码混淆
    [switch]$Compress = $true,           # UPX压缩
    [switch]$Verbose = $true,           # 详细输出
    [switch]$SkipTests = $false,         # 跳过测试
    [switch]$GenerateChecksums = $true   # 生成校验和
)

# 颜色输出函数
function Write-Info($message) { Write-Host "[INFO] $message" -ForegroundColor Green }
function Write-Success($message) { Write-Host "[SUCCESS] $message" -ForegroundColor Cyan }
function Write-Warning($message) { Write-Host "[WARN] $message" -ForegroundColor Yellow }
function Write-Error($message) { Write-Host "[ERROR] $message" -ForegroundColor Red }
function Write-Debug($message) { if ($Verbose) { Write-Host "[DEBUG] $message" -ForegroundColor Gray } }

# 全局变量
$script:BuildResults = @{}
$script:DistDir = "dist"
$script:ProjectName = "walmart-bind-card-processor"
$script:MainFiles = @{
    "processor" = "main.go"
}

# 支持的平台配置
$script:Platforms = @{
    "linux-amd64" = @{ OS = "linux"; Arch = "amd64"; Ext = "" }
    "linux-arm64" = @{ OS = "linux"; Arch = "arm64"; Ext = "" }
    "windows-amd64" = @{ OS = "windows"; Arch = "amd64"; Ext = ".exe" }
    "darwin-amd64" = @{ OS = "darwin"; Arch = "amd64"; Ext = "" }
    "darwin-arm64" = @{ OS = "darwin"; Arch = "arm64"; Ext = "" }
}

function Show-Header {
    Write-Host "================================================================" -ForegroundColor Magenta
    Write-Host "🚀 Walmart绑卡处理器 - 多平台构建脚本" -ForegroundColor Magenta
    Write-Host "================================================================" -ForegroundColor Magenta
    Write-Host ""
    Write-Info "项目: $script:ProjectName"
    Write-Info "平台: $Platform"
    Write-Info "版本: $(if ($Version) { $Version } else { '自动检测' })"
    Write-Info "混淆: $Obfuscate"
    Write-Info "压缩: $Compress"
    Write-Info "详细输出: $Verbose"
    Write-Host ""
}

function Get-ProjectVersion {
    if ($Version) {
        return $Version
    }
    
    # 尝试从git tag获取版本
    try {
        $gitTag = git describe --tags --exact-match HEAD 2>$null
        if ($gitTag) {
            Write-Debug "从git tag获取版本: $gitTag"
            return $gitTag
        }
    } catch {
        Write-Debug "无法从git tag获取版本"
    }
    
    # 尝试从git commit获取版本
    try {
        $gitCommit = git rev-parse --short HEAD 2>$null
        if ($gitCommit) {
            $version = "v1.0.0-$gitCommit"
            Write-Debug "从git commit生成版本: $version"
            return $version
        }
    } catch {
        Write-Debug "无法从git commit获取版本"
    }

    # 默认版本
    $defaultVersion = "v0.0.1-dev"
    Write-Warning "使用默认版本: $defaultVersion"
    return $defaultVersion
}

function Test-Prerequisites {
    Write-Info "检查构建环境..."

    # 检查Go环境
    try {
        $goVersion = go version 2>$null
        if (-not $goVersion) {
            throw "Go未安装或不在PATH中"
        }
        Write-Debug "Go版本: $goVersion"
    } catch {
        Write-Error "❌ Go环境检查失败: $($_.Exception.Message)"
        return $false
    }
    
    # 检查git（用于版本信息）
    try {
        $gitVersion = git --version 2>$null
        if ($gitVersion) {
            Write-Debug "Git版本: $gitVersion"
        }
    } catch {
        Write-Warning "Git未安装，将使用默认版本信息"
    }
    
    # 检查混淆工具（如果需要）
    if ($Obfuscate) {
        try {
            $garbleVersion = garble version 2>$null
            if (-not $garbleVersion) {
                Write-Warning "garble未安装，将使用标准构建"
                $script:Obfuscate = $false
            } else {
                Write-Debug "Garble版本: $garbleVersion"
            }
        } catch {
            Write-Warning "garble检查失败，将使用标准构建"
            $script:Obfuscate = $false
        }
    }

    # 检查UPX（如果需要压缩）
    if ($Compress) {
        try {
            $upxVersion = upx --version 2>$null
            if (-not $upxVersion) {
                Write-Warning "UPX未安装，跳过二进制压缩"
                $script:Compress = $false
            } else {
                Write-Debug "UPX可用"
            }
        } catch {
            Write-Warning "UPX检查失败，跳过二进制压缩"
            $script:Compress = $false
        }
    }
    
    Write-Success "✅ 环境检查完成"
    return $true
}

function Initialize-BuildEnvironment {
    Write-Info "初始化构建环境..."
    
    # 创建dist目录
    if (Test-Path $script:DistDir) {
        if ($Clean) {
            Write-Info "清理现有构建产物..."
            Remove-Item $script:DistDir -Recurse -Force
        }
    }
    
    if (-not (Test-Path $script:DistDir)) {
        New-Item -ItemType Directory -Path $script:DistDir | Out-Null
    }
    
    # 运行测试（如果需要）
    if (-not $SkipTests) {
        Write-Info "运行测试..."
        try {
            $testResult = go test ./... 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Success "✅ 测试通过"
            } else {
                Write-Warning "测试失败，但继续构建"
                Write-Debug "测试输出: $testResult"
            }
        } catch {
            Write-Warning "测试执行失败: $($_.Exception.Message)"
        }
    }
    
    Write-Success "✅ 构建环境初始化完成"
}

function Get-TargetPlatforms {
    $targetPlatforms = @()
    
    switch ($Platform.ToLower()) {
        "all" { 
            $targetPlatforms = $script:Platforms.Keys 
        }
        "linux" {
            $targetPlatforms = @("linux-amd64", "linux-arm64")
        }
        "windows" {
            $targetPlatforms = @("windows-amd64")
        }
        "darwin" {
            $targetPlatforms = @("darwin-amd64", "darwin-arm64")
        }
        default { 
            if ($script:Platforms.ContainsKey($Platform)) {
                $targetPlatforms = @($Platform)
            } else {
                Write-Error "❌ 不支持的平台: $Platform"
                Write-Info "支持的平台: $($script:Platforms.Keys -join ', ')"
                return @()
            }
        }
    }
    
    return $targetPlatforms
}

function Build-Binary {
    param(
        [string]$PlatformKey,
        [string]$BinaryName,
        [string]$MainFile,
        [string]$OutputDir
    )

    $platformConfig = $script:Platforms[$PlatformKey]
    $targetOS = $platformConfig.OS
    $targetArch = $platformConfig.Arch
    $ext = $platformConfig.Ext

    $outputFile = Join-Path $OutputDir "$BinaryName$ext"

    Write-Info "构建 $BinaryName ($PlatformKey)..."

    # 构建参数
    $buildTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $version = Get-ProjectVersion
    $ldflags = "-w -s -X `"main.Version=$version`" -X `"main.BuildTime=$buildTime`""

    # 备份环境变量
    $envBackup = @{
        GOOS = $env:GOOS
        GOARCH = $env:GOARCH
        CGO_ENABLED = $env:CGO_ENABLED
    }

    try {
        # 设置构建环境
        $env:GOOS = $targetOS
        $env:GOARCH = $targetArch
        $env:CGO_ENABLED = "0"

        Write-Debug "构建环境: GOOS=$env:GOOS, GOARCH=$env:GOARCH, CGO_ENABLED=$env:CGO_ENABLED"

        # 执行构建
        if ($Obfuscate) {
            Write-Debug "使用混淆构建: $outputFile"
            Write-Debug "ldflags: $ldflags"
            & garble -tiny -literals build "-ldflags=$ldflags" -trimpath -o $outputFile $MainFile
        } else {
            Write-Debug "使用标准构建: $outputFile"
            Write-Debug "ldflags: $ldflags"
            & go build "-ldflags=$ldflags" -trimpath -o $outputFile $MainFile
        }

        if ($LASTEXITCODE -ne 0) {
            throw "构建失败"
        }

        # 检查构建结果
        if (-not (Test-Path $outputFile)) {
            throw "构建文件不存在"
        }

        $fileInfo = Get-Item $outputFile
        Write-Debug "构建成功: $($fileInfo.Name) ($([math]::Round($fileInfo.Length / 1MB, 2)) MB)"

        # UPX压缩（如果启用且为Linux/Windows）
        if ($Compress -and ($targetOS -eq "linux" -or $targetOS -eq "windows")) {
            Write-Debug "压缩二进制文件..."
            try {
                & upx --best --lzma $outputFile 2>$null
                if ($LASTEXITCODE -eq 0) {
                    $compressedInfo = Get-Item $outputFile
                    Write-Debug "压缩完成: $([math]::Round($compressedInfo.Length / 1MB, 2)) MB"
                }
            } catch {
                Write-Warning "压缩失败，继续使用未压缩版本"
            }
        }

        return $true

    } catch {
        Write-Error "❌ 构建失败 ($PlatformKey): $($_.Exception.Message)"
        return $false

    } finally {
        # 恢复环境变量
        if ($envBackup.GOOS) { $env:GOOS = $envBackup.GOOS } else { Remove-Item Env:GOOS -ErrorAction SilentlyContinue }
        if ($envBackup.GOARCH) { $env:GOARCH = $envBackup.GOARCH } else { Remove-Item Env:GOARCH -ErrorAction SilentlyContinue }
        if ($envBackup.CGO_ENABLED) { $env:CGO_ENABLED = $envBackup.CGO_ENABLED } else { Remove-Item Env:CGO_ENABLED -ErrorAction SilentlyContinue }
    }
}

function Build-Platform {
    param([string]$PlatformKey)

    $platformConfig = $script:Platforms[$PlatformKey]
    $version = Get-ProjectVersion

    # 创建平台目录
    $platformDir = Join-Path $script:DistDir $PlatformKey
    if (-not (Test-Path $platformDir)) {
        New-Item -ItemType Directory -Path $platformDir | Out-Null
    }

    Write-Info "构建平台: $PlatformKey"

    $buildSuccess = $true
    $builtFiles = @()

    # 构建所有二进制文件
    foreach ($binaryName in $script:MainFiles.Keys) {
        $mainFile = $script:MainFiles[$binaryName]

        # 检查主文件是否存在
        if (-not (Test-Path $mainFile)) {
            Write-Warning "跳过 $binaryName`: 主文件 $mainFile 不存在"
            continue
        }

        $success = Build-Binary -PlatformKey $PlatformKey -BinaryName $binaryName -MainFile $mainFile -OutputDir $platformDir

        if ($success) {
            $ext = $platformConfig.Ext
            $builtFiles += "$binaryName$ext"
        } else {
            $buildSuccess = $false
        }
    }

    if (-not $buildSuccess) {
        Write-Error "❌ 平台 $PlatformKey 构建失败"
        return $false
    }

    # 复制配置文件和文档
    Copy-AdditionalFiles -PlatformDir $platformDir -PlatformKey $PlatformKey

    # 创建压缩包
    $archiveFile = Create-Archive -PlatformDir $platformDir -PlatformKey $PlatformKey -Version $version

    if ($archiveFile) {
        $script:BuildResults[$PlatformKey] = @{
            Success = $true
            ArchiveFile = $archiveFile
            BinaryFiles = $builtFiles
        }
        Write-Success "✅ 平台 $PlatformKey 构建成功"
        return $true
    } else {
        Write-Error "❌ 平台 $PlatformKey 打包失败"
        return $false
    }
}

function Copy-AdditionalFiles {
    param(
        [string]$PlatformDir,
        [string]$PlatformKey
    )

    Write-Debug "复制附加文件到 $PlatformDir..."

    # 复制配置文件
    if (Test-Path "config.yaml") {
        Copy-Item "config.yaml" $PlatformDir
        # 创建配置模板
        Copy-Item "config.yaml" (Join-Path $PlatformDir "config.yaml.example")
    }

    # 复制README
    if (Test-Path "README.md") {
        Copy-Item "README.md" $PlatformDir
    }

    # 复制文档目录
    if (Test-Path "docs") {
        Copy-Item "docs" $PlatformDir -Recurse -Force
    }

    # 复制Go模块文件
    if (Test-Path "go.mod") {
        Copy-Item "go.mod" $PlatformDir
    }
    if (Test-Path "go.sum") {
        Copy-Item "go.sum" $PlatformDir
    }

    # 复制Docker文件
    $dockerFiles = @("Dockerfile", "docker-compose.yml", "docker-compose.prod.yml")
    foreach ($file in $dockerFiles) {
        if (Test-Path $file) {
            Copy-Item $file $PlatformDir
        }
    }

    # 复制systemd服务文件
    if (Test-Path "walmart-bind-card-processor.service") {
        Copy-Item "walmart-bind-card-processor.service" $PlatformDir
    }

    # 创建启动脚本
    Create-StartupScripts -PlatformDir $PlatformDir -PlatformKey $PlatformKey

    # 创建安装脚本
    Create-InstallScripts -PlatformDir $PlatformDir -PlatformKey $PlatformKey

    Write-Debug "附加文件复制完成"
}



function Create-StartupScripts {
    param(
        [string]$PlatformDir,
        [string]$PlatformKey
    )

    $platformConfig = $script:Platforms[$PlatformKey]
    $isWindowsPlatform = $platformConfig.OS -eq "windows"

    if ($isWindowsPlatform) {
        # Windows批处理脚本
        $startScript = @"
@echo off
echo Starting Walmart Bind Card Processor...
echo =======================================

REM 检查配置文件
if not exist config.yaml (
    echo Error: config.yaml not found
    echo Please copy config.yaml.example to config.yaml and configure it
    pause
    exit /b 1
)

REM 启动处理器
echo Starting processor...
processor.exe

pause
"@
        $startScript | Out-File -FilePath (Join-Path $PlatformDir "start.bat") -Encoding ASCII

        # Windows开发模式脚本
        $devScript = @"
@echo off
echo Starting Walmart Bind Card Processor (Development Mode)...
echo ========================================================

REM 设置开发环境变量
set ENVIRONMENT=development
set DEBUG=true

REM 启动开发模式
echo Starting development mode...
echo Available commands:
echo   processor.exe     - Main processor

processor.exe

pause
"@
        $devScript | Out-File -FilePath (Join-Path $PlatformDir "start-dev.bat") -Encoding ASCII

    } else {
        # Unix shell脚本
        $startScript = @"
#!/bin/bash
echo "Starting Walmart Bind Card Processor..."
echo "======================================="

# 检查配置文件
if [ ! -f config.yaml ]; then
    echo "Error: config.yaml not found"
    echo "Please copy config.yaml.example to config.yaml and configure it"
    exit 1
fi

# 设置执行权限
chmod +x processor 2>/dev/null

# 启动处理器
echo "Starting processor..."
./processor
"@
        $startScript | Out-File -FilePath (Join-Path $PlatformDir "start.sh") -Encoding UTF8

        # Unix开发模式脚本
        $devScript = @"
#!/bin/bash
echo "Starting Walmart Bind Card Processor (Development Mode)..."
echo "=========================================================="

# 设置开发环境变量
export ENVIRONMENT=development
export DEBUG=true

# 设置执行权限
chmod +x processor 2>/dev/null

# 启动开发模式
echo "Starting development mode..."
echo "Available commands:"
echo "  ./processor         - Main processor"

./processor
"@
        $devScript | Out-File -FilePath (Join-Path $PlatformDir "start-dev.sh") -Encoding UTF8
    }

    Write-Debug "启动脚本创建完成"
}

function Create-InstallScripts {
    param(
        [string]$PlatformDir,
        [string]$PlatformKey
    )

    $platformConfig = $script:Platforms[$PlatformKey]
    $isWindowsPlatform = $platformConfig.OS -eq "windows"

    if ($isWindowsPlatform) {
        # Windows安装脚本
        $installScript = @"
@echo off
echo Installing Walmart Bind Card Processor as Windows Service...
echo ===========================================================

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Error: This script requires administrator privileges
    echo Please run as administrator
    pause
    exit /b 1
)

REM 获取当前目录
set INSTALL_DIR=%~dp0

REM 创建Windows服务
echo Creating Windows service...
sc create WalmartBindCardProcessor binPath= "\"%INSTALL_DIR%processor.exe\"" start= auto
if %errorLevel% equ 0 (
    echo Service created successfully
    echo Starting service...
    sc start WalmartBindCardProcessor
) else (
    echo Failed to create service
)

pause
"@
        $installScript | Out-File -FilePath (Join-Path $PlatformDir "install-service.bat") -Encoding ASCII

    } else {
        # Unix安装脚本
        $installScript = @"
#!/bin/bash
echo "Installing Walmart Bind Card Processor as systemd service..."
echo "============================================================"

# 检查root权限
if [ "\$EUID" -ne 0 ]; then
    echo "Error: This script requires root privileges"
    echo "Please run with sudo"
    exit 1
fi

# 获取当前目录
INSTALL_DIR="`$(cd "`$(dirname "`$0")" && pwd)"

# 设置执行权限
chmod +x "\$INSTALL_DIR/processor"

# 创建systemd服务文件
echo "Creating systemd service..."
cat > /etc/systemd/system/walmart-bind-card-processor.service << EOF
[Unit]
Description=Walmart Bind Card Processor
After=network.target mysql.service redis.service

[Service]
Type=simple
User=root
WorkingDirectory=\$INSTALL_DIR
ExecStart=\$INSTALL_DIR/processor
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

# 重新加载systemd并启用服务
systemctl daemon-reload
systemctl enable walmart-bind-card-processor
systemctl start walmart-bind-card-processor

echo "Service installed and started successfully"
echo "Use 'systemctl status walmart-bind-card-processor' to check status"
"@
        $installScript | Out-File -FilePath (Join-Path $PlatformDir "install-service.sh") -Encoding UTF8


    }

    Write-Debug "安装脚本创建完成"
}

function Create-Archive {
    param(
        [string]$PlatformDir,
        [string]$PlatformKey,
        [string]$Version
    )

    $platformConfig = $script:Platforms[$PlatformKey]
    $isWindowsPlatform = $platformConfig.OS -eq "windows"

    # 确定压缩包格式和文件名
    if ($isWindowsPlatform) {
        $archiveExt = "zip"
        $archiveFileName = "$script:ProjectName-$Version-$PlatformKey.$archiveExt"
        $archiveFile = Join-Path $script:DistDir $archiveFileName
    } else {
        $archiveExt = "tar.gz"
        $archiveFileName = "$script:ProjectName-$Version-$PlatformKey.$archiveExt"
        $archiveFile = Join-Path $script:DistDir $archiveFileName
    }

    Write-Debug "创建压缩包: $archiveFile"

    try {
        if ($isWindowsPlatform) {
            # 创建ZIP压缩包
            Compress-Archive -Path "$PlatformDir\*" -DestinationPath $archiveFile -Force
        } else {
            # 创建tar.gz压缩包
            $platformDirName = Split-Path $PlatformDir -Leaf
            $parentDir = Split-Path $PlatformDir -Parent

            Push-Location $parentDir
            try {
                if (Get-Command tar -ErrorAction SilentlyContinue) {
                    # 使用相对路径避免路径问题
                    Write-Debug "执行 tar 命令: tar -czf $archiveFileName $platformDirName"
                    & tar -czf $archiveFileName $platformDirName

                    if ($LASTEXITCODE -ne 0) {
                        throw "tar 命令执行失败，退出代码: $LASTEXITCODE"
                    }

                    # 检查文件是否创建成功
                    if (-not (Test-Path $archiveFileName)) {
                        throw "tar 命令执行后文件不存在"
                    }
                } else {
                    Write-Warning "tar 命令不可用，使用 PowerShell 压缩功能"
                    # 如果没有tar，使用PowerShell的压缩功能，但改为zip格式
                    $fallbackArchiveFileName = "$script:ProjectName-$Version-$PlatformKey.zip"
                    $fallbackArchiveFile = Join-Path $script:DistDir $fallbackArchiveFileName
                    Compress-Archive -Path "$PlatformDir\*" -DestinationPath $fallbackArchiveFile -Force
                    $archiveFile = $fallbackArchiveFile
                    $archiveFileName = $fallbackArchiveFileName
                }
            } finally {
                Pop-Location
            }
        }

        if (Test-Path $archiveFile) {
            $archiveInfo = Get-Item $archiveFile
            Write-Debug "压缩包创建成功: $($archiveInfo.Name) ($([math]::Round($archiveInfo.Length / 1MB, 2)) MB)"
            return $archiveFile
        } else {
            throw "压缩包文件不存在: $archiveFile"
        }

    } catch {
        Write-Error "创建压缩包失败: $($_.Exception.Message)"
        Write-Debug "压缩包路径: $archiveFile"
        Write-Debug "平台目录: $PlatformDir"

        # 尝试回退到 PowerShell 压缩
        if (-not $isWindowsPlatform) {
            try {
                Write-Warning "尝试使用 PowerShell 压缩作为回退方案"
                $fallbackArchiveFileName = "$script:ProjectName-$Version-$PlatformKey.zip"
                $fallbackArchiveFile = Join-Path $script:DistDir $fallbackArchiveFileName
                Compress-Archive -Path "$PlatformDir\*" -DestinationPath $fallbackArchiveFile -Force

                if (Test-Path $fallbackArchiveFile) {
                    $archiveInfo = Get-Item $fallbackArchiveFile
                    Write-Debug "回退压缩包创建成功: $($archiveInfo.Name) ($([math]::Round($archiveInfo.Length / 1MB, 2)) MB)"
                    return $fallbackArchiveFile
                }
            } catch {
                Write-Error "回退压缩也失败: $($_.Exception.Message)"
            }
        }

        return $null
    }
}



function Generate-Checksums {
    Write-Info "生成校验和文件..."

    $checksumFile = Join-Path $script:DistDir "checksums.txt"
    $checksums = @()

    # 为每个成功构建的压缩包生成SHA256校验和
    foreach ($platform in $script:BuildResults.Keys) {
        $result = $script:BuildResults[$platform]
        if ($result.Success -and $result.ArchiveFile -and (Test-Path $result.ArchiveFile)) {
            try {
                $hash = Get-FileHash $result.ArchiveFile -Algorithm SHA256
                $fileName = Split-Path $result.ArchiveFile -Leaf
                $checksums += "$($hash.Hash.ToLower())  $fileName"
                Write-Debug "校验和: $fileName -> $($hash.Hash.ToLower())"
            } catch {
                Write-Warning "生成 $platform 校验和失败: $($_.Exception.Message)"
            }
        }
    }

    if ($checksums.Count -gt 0) {
        $checksums | Out-File -FilePath $checksumFile -Encoding UTF8
        Write-Success "✅ 校验和文件已生成: checksums.txt"
    } else {
        Write-Warning "没有生成校验和文件"
    }
}

function Show-BuildSummary {
    Write-Host ""
    Write-Host "================================================================" -ForegroundColor Magenta
    Write-Host "🎯 构建完成总结" -ForegroundColor Magenta
    Write-Host "================================================================" -ForegroundColor Magenta
    Write-Host ""

    $successCount = 0
    $totalCount = $script:BuildResults.Count

    foreach ($platform in $script:BuildResults.Keys) {
        $result = $script:BuildResults[$platform]
        if ($result.Success) {
            $successCount++
            Write-Success "✅ $platform"
            if ($result.ArchiveFile -and (Test-Path $result.ArchiveFile)) {
                $archiveInfo = Get-Item $result.ArchiveFile
                Write-Info "   文件: $($archiveInfo.Name)"
                Write-Info "   大小: $([math]::Round($archiveInfo.Length / 1MB, 2)) MB"
            }
        } else {
            Write-Error "❌ $platform"
        }
    }

    Write-Host ""
    Write-Info "构建结果: $successCount/$totalCount 成功"

    if ($successCount -gt 0) {
        Write-Host ""
        Write-Info "构建产物位置: $script:DistDir/"

        # 显示所有生成的文件
        $allFiles = Get-ChildItem $script:DistDir -File | Sort-Object Name
        foreach ($file in $allFiles) {
            Write-Info "  $($file.Name) ($([math]::Round($file.Length / 1MB, 2)) MB)"
        }

        Write-Host ""
        Write-Info "部署方式:"
        Write-Info "  1. 传统部署: 解压对应平台的压缩包，运行 start.sh 或 start.bat"
        Write-Info "  2. 系统服务: 运行 install-service 脚本安装为系统服务"
        Write-Info "  3. 开发模式: 运行 start-dev 脚本启动开发环境"
        Write-Info "  4. 直接运行: 执行 processor 二进制文件"

        if (Test-Path (Join-Path $script:DistDir "checksums.txt")) {
            Write-Host ""
            Write-Info "校验和验证:"
            Write-Info "  使用 checksums.txt 验证文件完整性"
            Write-Info "  Linux/macOS: sha256sum -c checksums.txt"
            Write-Info "  Windows: Get-FileHash <file> -Algorithm SHA256"
        }
    }

    Write-Host ""
}

# 主执行逻辑
function Main {
    try {
        Show-Header

        # 检查环境
        if (-not (Test-Prerequisites)) {
            exit 1
        }

        # 初始化构建环境
        Initialize-BuildEnvironment

        # 获取目标平台
        $targetPlatforms = Get-TargetPlatforms
        if ($targetPlatforms.Count -eq 0) {
            exit 1
        }

        Write-Info "目标平台: $($targetPlatforms -join ', ')"
        Write-Host ""

        # 构建各个平台
        foreach ($platform in $targetPlatforms) {
            $success = Build-Platform -PlatformKey $platform
            if (-not $success) {
                Write-Warning "平台 $platform 构建失败，继续构建其他平台"
            }
            Write-Host ""
        }



        # 生成校验和
        if ($GenerateChecksums -and $script:BuildResults.Count -gt 0) {
            Generate-Checksums
        }

        # 显示构建总结
        Show-BuildSummary

        # 检查是否有构建失败
        $hasFailures = $false
        foreach ($result in $script:BuildResults.Values) {
            if (-not $result.Success) {
                $hasFailures = $true
                break
            }
        }

        if ($hasFailures) {
            Write-Warning "部分平台构建失败"
            exit 1
        } else {
            Write-Success "🎉 所有平台构建成功！"
            exit 0
        }

    } catch {
        Write-Error "构建过程中发生异常: $($_.Exception.Message)"
        Write-Debug "异常详情: $($_.Exception.ToString())"
        exit 1
    }
}

# 脚本入口点
if ($MyInvocation.InvocationName -ne '.') {
    Main
}
