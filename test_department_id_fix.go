package main

import (
	"encoding/json"
	"fmt"
	"log"
	"strings"

	"walmart-bind-card-processor/internal/config"
	"walmart-bind-card-processor/internal/model"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func main() {
	fmt.Println("🧪 检查部门ID修复效果...")

	// 加载配置
	cfg, err := config.LoadConfig("config.yaml")
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 构建数据库连接字符串
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		cfg.Database.User,
		cfg.Database.Password,
		cfg.Database.Host,
		cfg.Database.Port,
		cfg.Database.DBName)

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}

	// 查询最近的绑卡日志
	var logs []model.BindingLog
	err = db.Order("created_at DESC").
		Limit(10).
		Find(&logs).Error

	if err != nil {
		log.Fatalf("查询绑卡日志失败: %v", err)
	}

	fmt.Printf("\n📊 最近的 %d 条绑卡日志:\n", len(logs))
	fmt.Println(strings.Repeat("=", 80))

	departmentIDCount := 0
	for i, logEntry := range logs {
		fmt.Printf("日志 #%d:\n", i+1)
		fmt.Printf("  ID: %s\n", logEntry.ID)
		fmt.Printf("  日志类型: %s\n", logEntry.LogType)
		fmt.Printf("  消息: %s\n", logEntry.Message)

		if logEntry.MerchantID != nil {
			fmt.Printf("  商户ID: %d\n", *logEntry.MerchantID)
		} else {
			fmt.Printf("  商户ID: <空>\n")
		}

		if logEntry.DepartmentID != nil {
			fmt.Printf("  ✅ 部门ID: %d\n", *logEntry.DepartmentID)
			departmentIDCount++
		} else {
			fmt.Printf("  ❌ 部门ID: <空>\n")
		}

		if logEntry.WalmartCKID != nil {
			fmt.Printf("  CK_ID: %d\n", *logEntry.WalmartCKID)
		}

		// 解析详细信息
		if logEntry.Details != nil {
			var details map[string]interface{}
			if err := json.Unmarshal([]byte(*logEntry.Details), &details); err == nil {
				if deptID, exists := details["department_id"]; exists {
					fmt.Printf("  详细信息中的部门ID: %v\n", deptID)
				}
			}
		}

		fmt.Printf("  创建时间: %s\n", logEntry.CreatedAt.Format("2006-01-02 15:04:05"))
		fmt.Println("  " + strings.Repeat("-", 60))
	}

	// 总结测试结果
	fmt.Println("\n🎯 检查结果总结:")
	fmt.Printf("总日志数: %d\n", len(logs))
	fmt.Printf("包含部门ID的日志数: %d\n", departmentIDCount)

	if departmentIDCount > 0 {
		fmt.Printf("✅ 成功！有 %d 条日志包含部门ID\n", departmentIDCount)
	} else {
		fmt.Println("❌ 所有日志的部门ID都为空，修复可能未生效")
	}

	fmt.Printf("部门ID覆盖率: %.1f%%\n", float64(departmentIDCount)/float64(len(logs))*100)
}
