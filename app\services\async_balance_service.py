"""
异步余额获取服务
支持异步数据库会话的余额获取功能
"""

import asyncio
import logging
from typing import Dict, Any, Optional, Tuple, Union
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.walmart_api import WalmartAPI
from app.models.walmart_ck import WalmartCK
from app.utils.time_utils import get_current_time

logger = logging.getLogger(__name__)

# 重试配置
MAX_BALANCE_RETRIES = 3  # 最大重试次数（总共4次尝试）
RETRY_DELAYS = [1, 2, 4]  # 重试间隔（秒）
BALANCE_TIMEOUT = 10  # 单次API调用超时时间（秒）


class AsyncBalanceService:
    """异步余额获取服务 - 支持异步和同步数据库会话"""
    
    def __init__(self, db: Union[Session, AsyncSession]):
        self.db = db
        self.is_async = isinstance(db, AsyncSession)
    
    async def get_card_balance_with_retry(
        self,
        record,
        walmart_ck_id: int,
        trace_id: Optional[str] = None,
        debug: bool = False
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        带重试机制的余额获取（支持异步和同步会话）

        Args:
            record: 卡记录对象
            walmart_ck_id: 沃尔玛CK ID
            trace_id: 追踪ID
            debug: 测试模式标识，为True时返回模拟数据

        Returns:
            Tuple[bool, Dict[str, Any]]: (是否成功, 结果数据)
        """
        card_number = record.card_number
        record_id = record.id
        
        test_prefix = "[TEST_MODE] " if debug else ""
        logger.info(
            f"{test_prefix}[ASYNC_BALANCE_START] 开始获取卡余额 | "
            f"record_id={record_id} | card_number={card_number[:6]}*** | "
            f"walmart_ck_id={walmart_ck_id} | trace_id={trace_id} | "
            f"session_type={'async' if self.is_async else 'sync'} | debug={debug}"
        )
        
        # 获取沃尔玛CK配置
        try:
            walmart_ck = await self._get_walmart_ck_config(walmart_ck_id, record_id)
            if not walmart_ck:
                return False, {
                    "error_message": f"沃尔玛CK配置不存在: {walmart_ck_id}",
                    "retry_count": 0,
                    "total_attempts": 1
                }
        except Exception as e:
            logger.error(f"[ASYNC_BALANCE_CK_ERROR] 获取CK配置失败 | record_id={record_id} | error={e}")
            return False, {
                "error_message": f"获取CK配置失败: {str(e)}",
                "retry_count": 0,
                "total_attempts": 1
            }
        
        # 创建API实例
        try:
            walmart_api = self._create_walmart_api(walmart_ck, record_id)
        except Exception as e:
            logger.error(f"[ASYNC_BALANCE_API_ERROR] 创建API实例失败 | record_id={record_id} | error={e}")
            return False, {
                "error_message": f"创建API实例失败: {str(e)}",
                "retry_count": 0,
                "total_attempts": 1
            }
        
        # 执行重试逻辑
        last_error = None
        for attempt in range(MAX_BALANCE_RETRIES + 1):  # 总共4次尝试
            try:
                logger.info(
                    f"[ASYNC_BALANCE_ATTEMPT] 尝试获取余额 #{attempt + 1} | "
                    f"record_id={record_id} | card_number={card_number[:6]}***"
                )
                
                # 调用余额获取API
                balance_data = await self._fetch_balance_with_timeout(
                    walmart_api, card_number, record_id, debug
                )
                
                if balance_data:
                    # 成功获取余额
                    logger.info(
                        f"[ASYNC_BALANCE_SUCCESS] 余额获取成功 | record_id={record_id} | "
                        f"attempt={attempt + 1} | balance={balance_data.get('balance')} | "
                        f"cardBalance={balance_data.get('cardBalance')} | "
                        f"balanceCnt={balance_data.get('balanceCnt')}"
                    )
                    
                    result = {
                        "balance": balance_data.get("balance"),
                        "cardBalance": balance_data.get("cardBalance"),
                        "balanceCnt": balance_data.get("balanceCnt"),
                        "retry_count": attempt,
                        "total_attempts": attempt + 1
                    }
                    return True, result
                else:
                    # API返回空数据
                    last_error = "API返回空余额数据"
                    logger.warning(
                        f"[ASYNC_BALANCE_EMPTY] API返回空数据 | record_id={record_id} | attempt={attempt + 1}"
                    )
                    
            except Exception as e:
                last_error = str(e)
                logger.warning(
                    f"[ASYNC_BALANCE_ATTEMPT_FAILED] 余额获取尝试失败 | "
                    f"record_id={record_id} | attempt={attempt + 1} | error={e}"
                )
            
            # 如果不是最后一次尝试，等待后重试
            if attempt < MAX_BALANCE_RETRIES:
                delay = RETRY_DELAYS[attempt] if attempt < len(RETRY_DELAYS) else RETRY_DELAYS[-1]
                logger.info(
                    f"[ASYNC_BALANCE_RETRY_WAIT] 等待 {delay} 秒后重试 | "
                    f"record_id={record_id} | next_attempt={attempt + 2}"
                )
                await asyncio.sleep(delay)
        
        # 所有重试都失败
        error_message = f"余额获取失败，已重试{MAX_BALANCE_RETRIES}次: {last_error}"
        logger.error(
            f"[ASYNC_BALANCE_ALL_FAILED] 所有余额获取尝试都失败 | "
            f"record_id={record_id} | total_attempts={MAX_BALANCE_RETRIES + 1} | "
            f"final_error={last_error}"
        )
        
        return False, {
            "error_message": error_message,
            "retry_count": MAX_BALANCE_RETRIES,
            "total_attempts": MAX_BALANCE_RETRIES + 1
        }
    
    async def _get_walmart_ck_config(self, walmart_ck_id: int, record_id: int) -> Optional[WalmartCK]:
        """获取沃尔玛CK配置（支持异步和同步会话）"""
        try:
            if self.is_async:
                # 异步查询
                stmt = select(WalmartCK).where(WalmartCK.id == walmart_ck_id)
                result = await self.db.execute(stmt)
                walmart_ck = result.scalar_one_or_none()
            else:
                # 同步查询
                walmart_ck = self.db.query(WalmartCK).filter(WalmartCK.id == walmart_ck_id).first()
            
            if not walmart_ck:
                logger.error(f"[ASYNC_BALANCE_CK_NOT_FOUND] CK配置不存在 | walmart_ck_id={walmart_ck_id} | record_id={record_id}")
                return None
            
            if not walmart_ck.active:
                logger.warning(f"[ASYNC_BALANCE_CK_INACTIVE] CK已禁用 | walmart_ck_id={walmart_ck_id} | record_id={record_id}")
                # 注意：即使CK被禁用，仍然可以用来查询余额
            
            return walmart_ck
        except Exception as e:
            logger.error(f"[ASYNC_BALANCE_CK_QUERY_ERROR] 查询CK配置异常 | walmart_ck_id={walmart_ck_id} | record_id={record_id} | error={e}")
            return None
    
    def _prepare_api_request_params(self, walmart_ck: WalmartCK) -> Optional[Dict[str, Any]]:
        """准备API请求参数"""
        user_sign_raw = walmart_ck.sign
        sign_parts = user_sign_raw.split("#")
        if len(sign_parts) < 3:
            logger.error(f"沃尔玛CK {walmart_ck.id} 签名格式错误: {user_sign_raw}")
            return None

        return {
            "sign": sign_parts[0],
            "encryption_key": sign_parts[1],
            "version": sign_parts[2],
        }

    def _create_walmart_api(self, walmart_ck: WalmartCK, record_id: int) -> WalmartAPI:
        """创建沃尔玛API实例"""
        try:
            api_params = self._prepare_api_request_params(walmart_ck)
            if not api_params:
                logger.error(
                    f"[ASYNC_BALANCE_ERROR] 准备API参数失败 | "
                    f"record_id={record_id} | walmart_ck_id={walmart_ck.id}"
                )
                raise ValueError("准备API参数失败")

            # 使用遗留方法创建API实例以保持兼容性
            walmart_api = WalmartAPI.create_legacy(
                base_url="https://apicard.swiftpass.cn",
                encryption_key=api_params["encryption_key"],
                token=None,
                version=api_params["version"],
                sign=api_params["sign"]
            )
            logger.debug(f"[ASYNC_BALANCE_API_CREATED] API实例创建成功 | record_id={record_id} | ck_id={walmart_ck.id}")
            return walmart_api
        except Exception as e:
            logger.error(f"[ASYNC_BALANCE_API_CREATE_ERROR] API实例创建失败 | record_id={record_id} | ck_id={walmart_ck.id} | error={e}")
            raise
    
    async def _fetch_balance_with_timeout(
        self,
        walmart_api: WalmartAPI,
        card_number: str,
        record_id: int,
        debug: bool = False
    ) -> Optional[Dict[str, Any]]:
        """
        带超时的余额获取
        """
        try:
            # 使用asyncio.wait_for实现超时控制
            balance_data = await asyncio.wait_for(
                self._async_get_balance(walmart_api, card_number, debug),
                timeout=BALANCE_TIMEOUT
            )
            return balance_data
        except asyncio.TimeoutError:
            logger.warning(f"[ASYNC_BALANCE_TIMEOUT] 余额获取超时 | record_id={record_id} | timeout={BALANCE_TIMEOUT}s")
            raise Exception(f"余额获取超时（{BALANCE_TIMEOUT}秒）")
        except Exception as e:
            logger.error(f"[ASYNC_BALANCE_FETCH_ERROR] 余额获取异常 | record_id={record_id} | error={e}")
            raise
    
    async def _async_get_balance(self, walmart_api: WalmartAPI, card_number: str, debug: bool = False) -> Dict[str, Any]:
        """
        异步获取余额（包装同步API）
        """
        # 在线程池中执行同步API调用
        loop = asyncio.get_event_loop()
        balance_data = await loop.run_in_executor(
            None,
            walmart_api.get_card_balance_sync,
            card_number,
            debug  # 传递测试模式参数
        )
        return balance_data
