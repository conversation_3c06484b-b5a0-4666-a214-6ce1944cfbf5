import uuid
from sqlalchemy import Column, String, Integer, BigInteger, JSON, ForeignKey, DateTime, Float, Boolean
from sqlalchemy.orm import relationship, Mapped, mapped_column

from app.models.base import BaseModel, TimestampMixin


class CardStatus:
    """卡状态常量类"""
    PENDING = "pending"
    PROCESSING = "processing"
    SUCCESS = "success"
    FAILED = "failed"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"

    @classmethod
    def get_all_values(cls):
        """获取所有状态值"""
        return [cls.PENDING, cls.PROCESSING, cls.SUCCESS, cls.FAILED, cls.TIMEOUT, cls.CANCELLED]


class CallbackStatus:
    """回调状态常量类"""
    PENDING = "pending"
    SUCCESS = "success"
    FAILED = "failed"

    @classmethod
    def get_all_values(cls):
        """获取所有状态值"""
        return [cls.PENDING, cls.SUCCESS, cls.FAILED]


class CardRecord(BaseModel, TimestampMixin):
    """卡记录模型"""

    __tablename__ = "card_records"

    id: Mapped[uuid.UUID] = mapped_column(
        String(36),
        primary_key=True,
        default=uuid.uuid4,
        comment="记录ID（UUID）",
    )
    merchant_id = Column(BigInteger, ForeignKey("merchants.id"), nullable=False, comment="商家ID")
    department_id = Column(BigInteger, ForeignKey("departments.id"), nullable=True, comment="所属部门ID（绑卡成功后填入实际使用的CK所属部门）")
    walmart_ck_id = Column(BigInteger, ForeignKey("walmart_ck.id"), nullable=True, comment="使用的沃尔玛CK ID（绑卡成功后填入实际使用的CK ID）")
    merchant_order_id = Column(String(255), nullable=False, comment="商家订单号")
    amount = Column(Integer, nullable=False, comment="订单金额，单位：分")
    actual_amount = Column(Integer, nullable=True, comment="实际卡金额，单位：分")
    balance = Column(String(32), nullable=True, comment="balance字段，单位元，原始格式")
    cardBalance = Column(String(32), nullable=True, comment="cardBalance字段，单位元，原始格式")
    balanceCnt = Column(String(32), nullable=True, comment="balanceCnt字段，单位元，原始格式")
    card_number = Column(String(50), nullable=False, comment="卡号")
    card_password = Column(String(512), nullable=True, comment="卡密（加密存储）")
    status = Column(String(20), nullable=False, default=CardStatus.PENDING, comment="状态")
    request_id = Column(String(64), nullable=False, unique=True, comment="请求ID")
    trace_id = Column(String(64), nullable=True, comment="追踪ID，用于跟踪整个处理流程")
    request_data = Column(JSON, nullable=False, comment="请求数据")
    response_data = Column(JSON, nullable=True, comment="响应数据")
    error_message = Column(String(500), nullable=True, comment="错误信息")
    retry_count = Column(Integer, nullable=False, default=0, comment="重试次数")
    process_time = Column(Float, nullable=True, comment="处理时间(秒)")
    ip_address = Column(String(64), nullable=True, comment="IP地址")
    remark = Column(String(500), nullable=True, comment="备注")
    ext_data = Column(String(512), nullable=True, comment="扩展数据，回调时原样返回")
    is_test_mode = Column(Boolean, nullable=False, default=False, comment="是否为测试模式，用于并发测试时标识测试数据")
    callback_status = Column(String(20), nullable=False, default=CallbackStatus.PENDING, comment="回调状态")
    callback_result = Column(String(500), nullable=True, comment="回调结果描述")
    callback_time = Column(DateTime(timezone=True), nullable=True, comment="回调时间")

    # 关联关系
    merchant = relationship("Merchant", back_populates="card_records")
    department = relationship("Department", back_populates="card_records")
    walmart_ck = relationship("WalmartCK", back_populates="card_records")
    binding_logs = relationship("BindingLog", back_populates="card_record", cascade="all, delete-orphan")

    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "merchant_id": self.merchant_id,
            "department_id": self.department_id,
            "walmart_ck_id": self.walmart_ck_id,
            "merchant_order_id": self.merchant_order_id,
            "amount": self.amount,
            "actual_amount": self.actual_amount,
            "balance": self.balance,
            "cardBalance": self.cardBalance,
            "balanceCnt": self.balanceCnt,
            "card_number": self.card_number,
            "status": self.status,
            "request_id": self.request_id,
            "trace_id": self.trace_id,
            "request_data": self.request_data,
            "response_data": self.response_data,
            "error_message": self.error_message,
            "retry_count": self.retry_count,
            "process_time": self.process_time,
            "ip_address": self.ip_address,
            "remark": self.remark,
            "ext_data": self.ext_data,
            "is_test_mode": self.is_test_mode,
            "callback_status": self.callback_status,
            "callback_result": self.callback_result,
            "callback_time": self.callback_time.isoformat() if self.callback_time else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
