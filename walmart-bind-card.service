[Unit]
Description=Walmart Card Binding Server
Documentation=https://github.com/your-org/walmart-bind-card-server
After=network.target mysql.service rabbitmq-server.service
Wants=network.target
Requires=mysql.service rabbitmq-server.service

[Service]
Type=simple
User=walmart
Group=walmart
WorkingDirectory=/opt/walmart-bind-card-server
Environment=PYTHONPATH=/opt/walmart-bind-card-server
Environment=PYTHONUNBUFFERED=1
Environment=ENV=production

# 执行文件路径 - 根据实际情况修改
ExecStart=/opt/walmart-bind-card-server/walmart-bind-card-server
# 如果是Python脚本，使用以下配置：
# ExecStart=/usr/bin/python3 /opt/walmart-bind-card-server/app/main.py

# 优雅停止
ExecStop=/bin/kill -TERM $MAINPID
TimeoutStopSec=30

# 自动重启配置
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/walmart-bind-card-server/logs
ReadWritePaths=/opt/walmart-bind-card-server/data
ReadWritePaths=/var/log/walmart-bind-card

# 标准输出和错误输出
StandardOutput=journal
StandardError=journal
SyslogIdentifier=walmart-bind-card

# 健康检查 (可选)
# ExecStartPre=/opt/walmart-bind-card-server/scripts/health-check.sh

[Install]
WantedBy=multi-user.target
