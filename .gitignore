# 编译产物
walmart-test
walmart-test.exe
build/
dist/

# 测试结果文件
test_results_*.json
test_suite_report_*.txt

# 日志文件
*.log

# 配置文件（包含敏感信息）
config.json
.env
.env.local

# Go相关
*.mod.sum.bak
vendor/

# IDE相关
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统相关
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker相关
.dockerignore

# 临时文件
tmp/
temp/
*.tmp

# 结果目录
results/
reports/

# 备份文件
*.bak
*.backup
walmart-success.exe
