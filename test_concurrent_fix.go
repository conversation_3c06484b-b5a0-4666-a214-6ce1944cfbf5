package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"sync"
	"time"
)

// TestRequest 测试请求结构
type TestRequest struct {
	CardNumber     string `json:"card_number"`
	CardPassword   string `json:"card_password"`
	Amount         int    `json:"amount"`
	MerchantID     int    `json:"merchant_id"`
	DepartmentID   *int   `json:"department_id,omitempty"`
	ClientIP       string `json:"client_ip"`
	UserAgent      string `json:"user_agent"`
}

// TestResult 测试结果
type TestResult struct {
	RequestID int
	Success   bool
	Duration  time.Duration
	Error     string
	Response  string
}

func main() {
	fmt.Println("🚀 开始100并发绑卡测试 - 验证UUID冲突修复效果")
	fmt.Println(strings.Repeat("=", 60))

	// 测试配置
	const (
		concurrency = 100
		baseURL     = "http://localhost:21000"
		endpoint    = "/api/v1/bind-card"
	)

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 60 * time.Second,
	}

	// 结果收集
	results := make(chan TestResult, concurrency)
	var wg sync.WaitGroup

	// 记录开始时间
	startTime := time.Now()

	// 启动并发测试
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func(requestID int) {
			defer wg.Done()
			
			reqStart := time.Now()
			
			// 生成测试数据
			testReq := TestRequest{
				CardNumber:   fmt.Sprintf("2326%04d%04d%08d", time.Now().Unix()%10000, requestID%10000, requestID*1000),
				CardPassword: "123456",
				Amount:       100,
				MerchantID:   4,
				ClientIP:     "127.0.0.1",
				UserAgent:    "ConcurrencyTest/1.0",
			}

			// 发送请求
			success, response, err := sendBindCardRequest(client, baseURL+endpoint, testReq)
			
			duration := time.Since(reqStart)
			
			result := TestResult{
				RequestID: requestID,
				Success:   success,
				Duration:  duration,
				Response:  response,
			}
			
			if err != nil {
				result.Error = err.Error()
			}
			
			results <- result
		}(i + 1)
	}

	// 等待所有请求完成
	go func() {
		wg.Wait()
		close(results)
	}()

	// 收集结果
	var (
		successCount = 0
		failCount    = 0
		totalDuration time.Duration
		maxDuration   time.Duration
		minDuration   = time.Hour
		errors        = make(map[string]int)
	)

	fmt.Println("📊 实时结果统计:")
	fmt.Println(strings.Repeat("-", 60))

	for result := range results {
		if result.Success {
			successCount++
			fmt.Printf("✅ 请求 %03d: 成功 (耗时: %v)\n", result.RequestID, result.Duration)
		} else {
			failCount++
			fmt.Printf("❌ 请求 %03d: 失败 (耗时: %v) - %s\n", result.RequestID, result.Duration, result.Error)
			errors[result.Error]++
		}

		totalDuration += result.Duration
		if result.Duration > maxDuration {
			maxDuration = result.Duration
		}
		if result.Duration < minDuration {
			minDuration = result.Duration
		}
	}

	totalTime := time.Since(startTime)
	avgDuration := totalDuration / time.Duration(concurrency)

	// 输出最终统计
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("📈 最终测试结果统计")
	fmt.Println(strings.Repeat("=", 60))
	fmt.Printf("总请求数: %d\n", concurrency)
	fmt.Printf("成功请求: %d (%.1f%%)\n", successCount, float64(successCount)/float64(concurrency)*100)
	fmt.Printf("失败请求: %d (%.1f%%)\n", failCount, float64(failCount)/float64(concurrency)*100)
	fmt.Printf("总耗时: %v\n", totalTime)
	fmt.Printf("平均响应时间: %v\n", avgDuration)
	fmt.Printf("最快响应: %v\n", minDuration)
	fmt.Printf("最慢响应: %v\n", maxDuration)
	fmt.Printf("QPS: %.1f\n", float64(concurrency)/totalTime.Seconds())

	// 错误统计
	if len(errors) > 0 {
		fmt.Println("\n🔍 错误类型统计:")
		fmt.Println(strings.Repeat("-", 40))
		for errMsg, count := range errors {
			fmt.Printf("• %s: %d次\n", errMsg, count)
		}
	}

	// 判断测试结果
	fmt.Println("\n🎯 测试结论:")
	fmt.Println(strings.Repeat("-", 30))
	if successCount == concurrency {
		fmt.Println("🎉 测试通过! 所有请求都成功处理，UUID冲突问题已解决!")
	} else if successCount >= concurrency*0.95 {
		fmt.Printf("⚠️  测试基本通过! 成功率%.1f%%，仍有少量失败需要关注\n", float64(successCount)/float64(concurrency)*100)
	} else {
		fmt.Printf("❌ 测试失败! 成功率仅%.1f%%，需要进一步优化\n", float64(successCount)/float64(concurrency)*100)
	}
}

// sendBindCardRequest 发送绑卡请求
func sendBindCardRequest(client *http.Client, url string, req TestRequest) (bool, string, error) {
	// 序列化请求
	jsonData, err := json.Marshal(req)
	if err != nil {
		return false, "", fmt.Errorf("序列化请求失败: %w", err)
	}

	// 创建HTTP请求
	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return false, "", fmt.Errorf("创建请求失败: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("User-Agent", "ConcurrencyTest/1.0")

	// 发送请求
	resp, err := client.Do(httpReq)
	if err != nil {
		return false, "", fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, "", fmt.Errorf("读取响应失败: %w", err)
	}

	responseStr := string(body)

	// 判断是否成功
	if resp.StatusCode == 200 {
		return true, responseStr, nil
	}

	return false, responseStr, fmt.Errorf("HTTP %d: %s", resp.StatusCode, responseStr)
}
