package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/go-redis/redis/v8"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func main() {
	fmt.Println("=== 验证重试修复效果 ===")
	
	// 连接数据库
	db, err := gorm.Open(mysql.Open("root:123456@tcp(localhost:3306)/walmart_bind_card?charset=utf8mb4&parseTime=True&loc=Local"), &gorm.Config{})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}
	
	// 连接Redis
	rdb := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   0,
	})
	
	ctx := context.Background()
	
	// 测试场景1：processing状态的记录应该允许重试
	fmt.Println("\n=== 测试场景1：processing状态允许重试 ===")
	testProcessingStatusRetry(db, ctx)
	
	// 测试场景2：success状态的记录应该跳过重试
	fmt.Println("\n=== 测试场景2：success状态跳过重试 ===")
	testSuccessStatusSkip(db, ctx)
	
	// 测试场景3：failed状态的记录应该允许重试
	fmt.Println("\n=== 测试场景3：failed状态允许重试 ===")
	testFailedStatusRetry(db, ctx)
	
	fmt.Println("\n=== 测试完成 ===")
}

func testProcessingStatusRetry(db *gorm.DB, ctx context.Context) {
	recordID := fmt.Sprintf("TEST_PROCESSING_%d", time.Now().Unix())
	merchantID := 1
	
	// 创建processing状态的记录
	db.Exec(`
		INSERT INTO card_records (
			id, merchant_id, merchant_order_id, card_number, card_password, amount, 
			status, request_id, request_data, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
	`, recordID, merchantID, "ORDER_"+recordID, "2326140000001234", "123456", 100, "processing", recordID, "{}")
	
	// 检查去重结果
	isDuplicate := checkMessageDuplicationByDBStatus(db, recordID, merchantID)
	fmt.Printf("记录ID: %s, 状态: processing, 去重检查结果: %t (应该为false，允许重试)\n", recordID, isDuplicate)
	
	// 清理测试数据
	db.Exec("DELETE FROM card_records WHERE id = ?", recordID)
}

func testSuccessStatusSkip(db *gorm.DB, ctx context.Context) {
	recordID := fmt.Sprintf("TEST_SUCCESS_%d", time.Now().Unix())
	merchantID := 1
	
	// 创建success状态的记录
	db.Exec(`
		INSERT INTO card_records (
			id, merchant_id, merchant_order_id, card_number, card_password, amount, 
			status, request_id, request_data, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
	`, recordID, merchantID, "ORDER_"+recordID, "2326140000001234", "123456", 100, "success", recordID, "{}")
	
	// 检查去重结果
	isDuplicate := checkMessageDuplicationByDBStatus(db, recordID, merchantID)
	fmt.Printf("记录ID: %s, 状态: success, 去重检查结果: %t (应该为true，跳过重试)\n", recordID, isDuplicate)
	
	// 清理测试数据
	db.Exec("DELETE FROM card_records WHERE id = ?", recordID)
}

func testFailedStatusRetry(db *gorm.DB, ctx context.Context) {
	recordID := fmt.Sprintf("TEST_FAILED_%d", time.Now().Unix())
	merchantID := 1
	
	// 创建failed状态的记录
	db.Exec(`
		INSERT INTO card_records (
			id, merchant_id, merchant_order_id, card_number, card_password, amount, 
			status, request_id, request_data, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
	`, recordID, merchantID, "ORDER_"+recordID, "2326140000001234", "123456", 100, "failed", recordID, "{}")
	
	// 检查去重结果
	isDuplicate := checkMessageDuplicationByDBStatus(db, recordID, merchantID)
	fmt.Printf("记录ID: %s, 状态: failed, 去重检查结果: %t (应该为false，允许重试)\n", recordID, isDuplicate)
	
	// 清理测试数据
	db.Exec("DELETE FROM card_records WHERE id = ?", recordID)
}

// 模拟基于数据库状态的去重检查
func checkMessageDuplicationByDBStatus(db *gorm.DB, recordID string, merchantID int) bool {
	var record struct {
		Status string `gorm:"column:status"`
	}
	
	err := db.Table("card_records").
		Select("status").
		Where("id = ? AND merchant_id = ?", recordID, merchantID).
		First(&record).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return false
		}
		fmt.Printf("查询记录状态失败: %v\n", err)
		return false
	}
	
	// 如果记录状态已经是成功，则认为是重复消息
	return record.Status == "success"
}
