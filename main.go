package main

import (
	"bytes"
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"os"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/fatih/color"
	"github.com/spf13/cobra"
)

// TestConfig 测试配置
type TestConfig struct {
	BaseURL        string
	APIKey         string
	APISecret      string
	MerchantCode   string
	Concurrency    int
	Duration       int
	TotalRequests  int
	Cycles         int  // 新增：循环次数
	Timeout        int
	Debug          bool
}

// ConfigFile 配置文件结构
type ConfigFile struct {
	APIConfig struct {
		BaseURL      string `json:"base_url"`
		APIKey       string `json:"api_key"`
		APISecret    string `json:"api_secret"`
		MerchantCode string `json:"merchant_code"`
	} `json:"api_config"`
	CardRules struct {
		Prefix      string `json:"prefix"`
		Description string `json:"description"`
		MinLength   int    `json:"min_length"`
		Format      string `json:"format"`
		Example     string `json:"example"`
	} `json:"card_rules"`
	TestScenarios []struct {
		Name          string `json:"name"`
		Description   string `json:"description"`
		Concurrency   int    `json:"concurrency"`
		TotalRequests int    `json:"total_requests,omitempty"`
		Duration      int    `json:"duration,omitempty"`
		Timeout       int    `json:"timeout"`
	} `json:"test_scenarios"`
	ExpectedResults struct {
		SuccessRateThreshold      float64 `json:"success_rate_threshold"`
		AvgResponseTimeThreshold  int     `json:"avg_response_time_threshold"`
		MaxResponseTimeThreshold  int     `json:"max_response_time_threshold"`
	} `json:"expected_results"`
}

// BindCardRequest 绑卡请求结构
type BindCardRequest struct {
	MerchantCode     string      `json:"merchant_code"`
	MerchantOrderID  string      `json:"merchant_order_id"`
	CardNumber       string      `json:"card_number"`
	CardPassword     string      `json:"card_password"`
	Amount           int         `json:"amount"`                    // 改为int类型
	ExtData          interface{} `json:"ext_data,omitempty"`       // 添加omitempty，nil时不序列化
	Debug            bool        `json:"debug,omitempty"`
}

// TestResult 测试结果
type TestResult struct {
	RequestID         int           `json:"request_id"`
	CardNumber        string        `json:"card_number"`
	StatusCode        int           `json:"status_code"`
	Duration          time.Duration `json:"duration"`
	Success           bool          `json:"success"`
	Error             string        `json:"error,omitempty"`
	ResponseBody      string        `json:"response_body,omitempty"`
	Timestamp         time.Time     `json:"timestamp"`
	GoroutineStartTime time.Time    `json:"goroutine_start_time"` // goroutine启动时间
	ActualStartTime   time.Time     `json:"actual_start_time"`    // 实际开始执行时间
	WaitDuration      time.Duration `json:"wait_duration"`        // 等待时间（信号量等待）
}

// TestSummary 测试摘要
type TestSummary struct {
	TotalRequests    int           `json:"total_requests"`
	SuccessRequests  int           `json:"success_requests"`
	FailedRequests   int           `json:"failed_requests"`
	SuccessRate      float64       `json:"success_rate"`
	TotalDuration    time.Duration `json:"total_duration"`
	AvgResponseTime  time.Duration `json:"avg_response_time"`
	MinResponseTime  time.Duration `json:"min_response_time"`
	MaxResponseTime  time.Duration `json:"max_response_time"`
	QPS              float64       `json:"qps"`
	Concurrency      int           `json:"concurrency"`
	StartTime        time.Time     `json:"start_time"`
	EndTime          time.Time     `json:"end_time"`
}

var (
	// 颜色输出
	colorRed    = color.New(color.FgRed).SprintFunc()
	colorGreen  = color.New(color.FgGreen).SprintFunc()
	colorYellow = color.New(color.FgYellow).SprintFunc()
	colorBlue   = color.New(color.FgBlue).SprintFunc()
	colorCyan   = color.New(color.FgCyan).SprintFunc()
)

// loadConfigFile 加载配置文件
func loadConfigFile(filename string) (*ConfigFile, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	var config ConfigFile
	err = json.Unmarshal(data, &config)
	if err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	return &config, nil
}

// mergeConfigWithFlags 合并配置文件和命令行参数
func mergeConfigWithFlags(fileConfig *ConfigFile, cliConfig TestConfig) TestConfig {
	// 优先使用命令行参数，如果没有则使用配置文件的值
	result := cliConfig

	if result.BaseURL == "http://localhost:8000" { // 默认值
		result.BaseURL = fileConfig.APIConfig.BaseURL
	}
	if result.APIKey == "" {
		result.APIKey = fileConfig.APIConfig.APIKey
	}
	if result.APISecret == "" {
		result.APISecret = fileConfig.APIConfig.APISecret
	}
	if result.MerchantCode == "" {
		result.MerchantCode = fileConfig.APIConfig.MerchantCode
	}

	return result
}

// validateConfig 验证配置是否完整
func validateConfig(config TestConfig) error {
	if config.APIKey == "" {
		return fmt.Errorf("API密钥不能为空")
	}
	if config.APISecret == "" {
		return fmt.Errorf("API密文不能为空")
	}
	if config.MerchantCode == "" {
		return fmt.Errorf("商户代码不能为空")
	}
	if config.BaseURL == "" {
		return fmt.Errorf("API基础URL不能为空")
	}
	return nil
}

func main() {
	var config TestConfig
	var configFile string

	var rootCmd = &cobra.Command{
		Use:   "walmart-concurrency-test",
		Short: "沃尔玛绑卡系统并发测试工具",
		Long: `
🚀 沃尔玛绑卡系统并发测试工具

用于测试Python沃尔玛绑卡系统的绑卡API接口并发处理能力，
验证事件循环阻塞问题是否已解决。

特性：
- 支持从配置文件读取参数
- 支持可配置的并发数量
- 生成唯一卡号避免重复
- 正确的API签名计算（排除debug参数）
- 详细的性能统计报告
- 实时进度显示
`,
		Run: func(cmd *cobra.Command, args []string) {
			// 尝试加载配置文件
			if configFile != "" {
				fileConfig, err := loadConfigFile(configFile)
				if err != nil {
					fmt.Printf("%s %v\n", colorRed("配置文件加载失败:"), err)
					fmt.Printf("%s 将使用命令行参数或默认值\n", colorYellow("警告:"))
				} else {
					// 合并配置文件和命令行参数
					config = mergeConfigWithFlags(fileConfig, config)
					fmt.Printf("%s 已加载配置文件: %s\n", colorGreen("✅"), configFile)
				}
			}

			// 验证必要的配置
			if err := validateConfig(config); err != nil {
				fmt.Printf("%s %v\n", colorRed("配置验证失败:"), err)
				fmt.Printf("%s 请检查配置文件或提供必要的命令行参数\n", colorYellow("提示:"))
				os.Exit(1)
			}

			runConcurrencyTest(config)
		},
	}

	// 添加命令行参数
	rootCmd.Flags().StringVarP(&configFile, "config", "f", "config.json", "配置文件路径")
	rootCmd.Flags().StringVarP(&config.BaseURL, "url", "u", "http://localhost:8000", "API基础URL")
	rootCmd.Flags().StringVarP(&config.APIKey, "key", "k", "", "API密钥")
	rootCmd.Flags().StringVarP(&config.APISecret, "secret", "s", "", "API密文")
	rootCmd.Flags().StringVarP(&config.MerchantCode, "merchant", "m", "", "商户代码")
	rootCmd.Flags().IntVarP(&config.Concurrency, "concurrency", "c", 10, "并发数量")
	rootCmd.Flags().IntVarP(&config.Duration, "duration", "d", 0, "测试持续时间（秒），0表示使用循环模式")
	rootCmd.Flags().IntVarP(&config.TotalRequests, "requests", "r", 50, "总请求数量（当duration=0且cycles=0时使用）")
	rootCmd.Flags().IntVarP(&config.Cycles, "cycles", "l", 0, "循环次数（每轮发送concurrency个并发请求），0表示使用requests模式")
	rootCmd.Flags().IntVarP(&config.Timeout, "timeout", "t", 30, "请求超时时间（秒）")
	rootCmd.Flags().BoolVar(&config.Debug, "debug", true, "启用调试模式")

	// 配置文件存在时，不强制要求命令行参数
	// rootCmd.MarkFlagRequired("key")
	// rootCmd.MarkFlagRequired("secret")
	// rootCmd.MarkFlagRequired("merchant")

	if err := rootCmd.Execute(); err != nil {
		fmt.Println(colorRed("Error:"), err)
		os.Exit(1)
	}
}

// generateUniqueCardNumber 生成唯一卡号
func generateUniqueCardNumber(requestID int) string {
	// 根据沃尔玛绑卡系统要求，卡号必须以"2326"开头
	// 使用时间戳和请求ID确保唯一性
	timestamp := time.Now().Unix()
	// 格式：2326 + 时间戳后4位 + 请求ID(4位) + 随机数(8位)
	return fmt.Sprintf("2326%04d%04d%08d", timestamp%10000, requestID%10000, rand.Intn(100000000))
}

// calculateSignature 计算API签名（排除debug参数）
func calculateSignature(params map[string]string, apiSecret string) string {
	// 排除debug参数
	signParams := make(map[string]string)
	for k, v := range params {
		if k != "debug" {
			signParams[k] = v
		}
	}

	// 按键名排序
	var keys []string
	for k := range signParams {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 构建签名字符串
	var signStr strings.Builder
	for i, k := range keys {
		if i > 0 {
			signStr.WriteString("&")
		}
		signStr.WriteString(fmt.Sprintf("%s=%s", k, signParams[k]))
	}
	signStr.WriteString("&key=")
	signStr.WriteString(apiSecret)

	// MD5加密并转大写
	hash := md5.Sum([]byte(signStr.String()))
	return strings.ToUpper(fmt.Sprintf("%x", hash))
}

// calculateHeaderSignature 计算请求头签名（按照服务器端算法）
func calculateHeaderSignature(requestData map[string]interface{}, timestamp, nonce, apiSecret string) string {
	// 按照服务器端的签名算法：METHOD|PATH|TIMESTAMP|NONCE|JSON_STRING|SECRET_KEY
	method := "POST"
	path := "/api/v1/card-bind"

	// 规范化数据：按键排序并转换为JSON字符串（与服务器端一致）
	jsonStr, err := json.Marshal(requestData)
	if err != nil {
		return ""
	}

	// 重新解析并按键排序
	var normalizedData map[string]interface{}
	json.Unmarshal(jsonStr, &normalizedData)

	// 生成排序的JSON字符串（不转义Unicode，与服务器端一致）
	sortedJsonBytes, err := json.Marshal(normalizedData)
	if err != nil {
		return ""
	}
	sortedJsonStr := string(sortedJsonBytes)

	// 构建签名字符串（使用竖线分隔）
	signatureComponents := []string{method, path, timestamp, nonce, sortedJsonStr, apiSecret}
	signStr := strings.Join(signatureComponents, "|")

	// 调试输出（仅在调试模式下）
	// fmt.Printf("🔍 签名计算详情:\n")
	// fmt.Printf("  方法: %s\n", method)
	// fmt.Printf("  路径: %s\n", path)
	// fmt.Printf("  时间戳: %s\n", timestamp)
	// fmt.Printf("  随机数: %s\n", nonce)
	// fmt.Printf("  JSON字符串: %s\n", sortedJsonStr)
	// fmt.Printf("  签名字符串: %s\n", signStr)
	// fmt.Printf("  签名字符串长度: %d\n", len(signStr))

	// 使用HMAC-SHA256生成签名并Base64编码
	h := hmac.New(sha256.New, []byte(apiSecret))
	h.Write([]byte(signStr))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))

	// fmt.Printf("  最终签名: %s\n", signature)

	return signature
}

// createBindCardRequest 创建绑卡请求
func createBindCardRequest(config TestConfig, requestID int) BindCardRequest {
	cardNumber := generateUniqueCardNumber(requestID)
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)  // 请求体中使用秒级时间戳
	merchantOrderID := fmt.Sprintf("TEST_%d_%s", requestID, timestamp)

	// 直接返回请求结构（不再需要预先计算签名）
	return BindCardRequest{
		MerchantCode:     config.MerchantCode,
		MerchantOrderID:  merchantOrderID,
		CardNumber:       cardNumber,
		CardPassword:     "123456",
		Amount:           100,    // 使用整数
		ExtData:          nil,   // 设置为nil
		Debug:            config.Debug,
	}
}

// sendBindCardRequest 发送绑卡请求
func sendBindCardRequest(config TestConfig, request BindCardRequest, requestID int) TestResult {
	startTime := time.Now()
	result := TestResult{
		RequestID:  requestID,
		CardNumber: request.CardNumber,
		Timestamp:  startTime,
	}
	return sendBindCardRequestInternal(config, request, result)
}

// sendBindCardRequestWithTiming 发送绑卡请求（带时间统计）
func sendBindCardRequestWithTiming(config TestConfig, request BindCardRequest, requestID int, goroutineStartTime, batchStartTime time.Time) TestResult {
	actualStartTime := time.Now()
	waitDuration := actualStartTime.Sub(goroutineStartTime)

	result := TestResult{
		RequestID:         requestID,
		CardNumber:        request.CardNumber,
		Timestamp:         actualStartTime,
		GoroutineStartTime: goroutineStartTime,
		ActualStartTime:   actualStartTime,
		WaitDuration:      waitDuration,
	}
	return sendBindCardRequestInternal(config, request, result)
}

// sendBindCardRequestInternal 内部发送请求实现
func sendBindCardRequestInternal(config TestConfig, request BindCardRequest, result TestResult) TestResult {
	startTime := result.ActualStartTime
	if startTime.IsZero() {
		startTime = time.Now()
		result.ActualStartTime = startTime
	}

	// 序列化请求体
	jsonData, err := json.Marshal(request)
	if err != nil {
		result.Error = fmt.Sprintf("JSON序列化失败: %v", err)
		result.Duration = time.Since(startTime)
		return result
	}

	// 创建HTTP请求
	url := fmt.Sprintf("%s/api/v1/card-bind", config.BaseURL)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		result.Error = fmt.Sprintf("创建请求失败: %v", err)
		result.Duration = time.Since(startTime)
		return result
	}

	// 生成请求头参数（使用毫秒级时间戳）
	timestamp := strconv.FormatInt(time.Now().UnixMilli(), 10)
	nonce := fmt.Sprintf("%d%d", time.Now().UnixNano(), rand.Intn(10000))

	// 将请求转换为map用于签名计算（与服务器端期望的格式完全一致）
	requestMap := map[string]interface{}{
		"merchant_code":      request.MerchantCode,
		"merchant_order_id":  request.MerchantOrderID,
		"card_number":        request.CardNumber,
		"card_password":      request.CardPassword,
		"amount":             request.Amount,  // 整数类型
	}
	// 注意：ext_data为nil时不参与签名计算（服务器端会排除None值）
	// 注意：debug字段不参与签名计算（服务器端会排除）

	// 计算请求头签名
	headerSignature := calculateHeaderSignature(requestMap, timestamp, nonce, config.APISecret)

	// 调试输出签名信息
	// if config.Debug {
	// 	fmt.Printf("🔍 签名调试信息:\n")
	// 	fmt.Printf("  时间戳: %s\n", timestamp)
	// 	fmt.Printf("  随机数: %s\n", nonce)
	// 	fmt.Printf("  请求数据: %+v\n", requestMap)
	// 	fmt.Printf("  生成的签名: %s\n", headerSignature)
	// 	fmt.Printf("  API密钥: %s\n", config.APISecret[:10]+"...")
	// }

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("api-key", config.APIKey)
	req.Header.Set("X-Timestamp", timestamp)
	req.Header.Set("X-Nonce", nonce)
	req.Header.Set("X-Signature", headerSignature)

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: time.Duration(config.Timeout) * time.Second,
	}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		result.Error = fmt.Sprintf("请求失败: %v", err)
		result.Duration = time.Since(startTime)
		return result
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		result.Error = fmt.Sprintf("读取响应失败: %v", err)
		result.Duration = time.Since(startTime)
		result.StatusCode = resp.StatusCode
		return result
	}

	result.StatusCode = resp.StatusCode
	result.ResponseBody = string(body)
	result.Duration = time.Since(startTime)
	result.Success = resp.StatusCode == 200

	return result
}

// runConcurrencyTest 运行并发测试
func runConcurrencyTest(config TestConfig) {
	fmt.Println(colorBlue("🚀 沃尔玛绑卡系统并发测试工具"))
	fmt.Println(colorBlue(strings.Repeat("=", 60)))

	// 显示测试配置
	printTestConfig(config)

	// 初始化随机数生成器（Go 1.20+ 推荐方式）
	// rand.Seed(time.Now().UnixNano()) // 已弃用，Go 1.20+ 会自动初始化

	var results []TestResult
	var mu sync.Mutex
	var wg sync.WaitGroup

	startTime := time.Now()

	if config.Duration > 0 {
		// 持续时间模式
		runDurationBasedTest(config, &results, &mu, &wg, startTime)
	} else if config.Cycles > 0 {
		// 循环并发模式
		runCycleBasedTest(config, &results, &mu, &wg, startTime)
	} else {
		// 固定请求数模式
		runRequestBasedTest(config, &results, &mu, &wg, startTime)
	}

	// 等待所有请求完成
	wg.Wait()
	endTime := time.Now()

	// 分析和显示结果
	summary := analyzeResults(results, config.Concurrency, startTime, endTime)
	printTestResults(summary, results)
}

// runDurationBasedTest 运行基于持续时间的测试
func runDurationBasedTest(config TestConfig, results *[]TestResult, mu *sync.Mutex, wg *sync.WaitGroup, startTime time.Time) {
	fmt.Printf("%s 开始持续时间测试: %d秒, %d并发\n", colorGreen("▶"), config.Duration, config.Concurrency)

	// 创建信号量控制并发数
	semaphore := make(chan struct{}, config.Concurrency)
	requestID := 0

	// 持续发送请求直到时间结束
	for time.Since(startTime) < time.Duration(config.Duration)*time.Second {
		requestID++

		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 创建并发送请求
			request := createBindCardRequest(config, id)
			result := sendBindCardRequest(config, request, id)

			// 保存结果
			mu.Lock()
			*results = append(*results, result)
			mu.Unlock()

			// 显示进度
			printProgress(id, result)
		}(requestID)

		// 控制请求发送频率，避免过快
		time.Sleep(time.Millisecond * 100)
	}
}

// runCycleBasedTest 运行基于循环的并发测试
func runCycleBasedTest(config TestConfig, results *[]TestResult, mu *sync.Mutex, wg *sync.WaitGroup, startTime time.Time) {
	fmt.Printf("%s 开始循环并发测试: %d个并发 × %d轮循环 = %d个总请求\n",
		colorGreen("▶"), config.Concurrency, config.Cycles, config.Concurrency*config.Cycles)

	requestID := 0

	// 循环执行指定次数
	for cycle := 1; cycle <= config.Cycles; cycle++ {
		fmt.Printf("\n%s 第 %d/%d 轮并发测试开始...\n",
			colorCyan("🔄"), cycle, config.Cycles)

		cycleStartTime := time.Now()
		var cycleWg sync.WaitGroup

		// 记录所有goroutine的启动时间用于并发验证
		batchStartTime := time.Now()

		// 每轮发送指定数量的并发请求
		for i := 0; i < config.Concurrency; i++ {
			requestID++
			cycleWg.Add(1)
			wg.Add(1)

			go func(reqID int, cycleNum int, batchStart time.Time) {
				defer cycleWg.Done()
				defer wg.Done()

				goroutineStartTime := time.Now()

				// 创建并发送请求
				request := createBindCardRequest(config, reqID)
				result := sendBindCardRequestWithTiming(config, request, reqID, goroutineStartTime, batchStart)

				// 保存结果
				mu.Lock()
				*results = append(*results, result)
				mu.Unlock()

				// 显示进度
				printCycleProgressWithTiming(reqID, cycleNum, result)
			}(requestID, cycle, batchStartTime)
		}

		// 等待当前轮次的所有请求完成
		cycleWg.Wait()
		cycleDuration := time.Since(cycleStartTime)

		// 显示轮次完成信息和并发性分析
		fmt.Printf("%s 第 %d 轮完成，耗时: %v\n",
			colorGreen("✅"), cycle, cycleDuration.Round(time.Millisecond))

		// 分析本轮的并发性
		analyzeConcurrency(*results, cycle, config.Concurrency)

		// 轮次间隔（避免对服务器造成过大压力）
		if cycle < config.Cycles {
			fmt.Printf("%s 等待 1 秒后开始下一轮...\n", colorYellow("⏳"))
			time.Sleep(time.Second)
		}
	}

	fmt.Printf("\n%s 所有 %d 轮循环并发测试完成！\n", colorGreen("🎉"), config.Cycles)
}

// runRequestBasedTest 运行基于请求数量的测试
func runRequestBasedTest(config TestConfig, results *[]TestResult, mu *sync.Mutex, wg *sync.WaitGroup, startTime time.Time) {
	fmt.Printf("%s 开始固定请求数测试: %d个请求, %d并发\n", colorGreen("▶"), config.TotalRequests, config.Concurrency)

	// 创建信号量控制并发数
	semaphore := make(chan struct{}, config.Concurrency)

	for i := 1; i <= config.TotalRequests; i++ {
		wg.Add(1)
		go func(requestID int) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 创建并发送请求
			request := createBindCardRequest(config, requestID)
			result := sendBindCardRequest(config, request, requestID)

			// 保存结果
			mu.Lock()
			*results = append(*results, result)
			mu.Unlock()

			// 显示进度
			printProgress(requestID, result)
		}(i)
	}
}

// printProgress 显示进度
func printProgress(requestID int, result TestResult) {
	status := colorRed("FAIL")
	if result.Success {
		status = colorGreen("OK")
	}

	fmt.Printf("[%s] 请求 #%d | 卡号: %s | 状态: %s | 耗时: %v | HTTP: %d\n",
		time.Now().Format("15:04:05"),
		requestID,
		result.CardNumber[len(result.CardNumber)-8:], // 显示卡号后8位
		status,
		result.Duration.Round(time.Millisecond),
		result.StatusCode,
	)

	if !result.Success && result.Error != "" {
		fmt.Printf("    %s %s\n", colorRed("错误:"), result.Error)
	}
}

// printCycleProgress 显示循环测试进度
func printCycleProgress(requestID int, cycleNum int, result TestResult) {
	status := colorRed("FAIL")
	if result.Success {
		status = colorGreen("OK")
	}

	fmt.Printf("[%s] 第%d轮 请求 #%d | 卡号: %s | 状态: %s | 耗时: %v | HTTP: %d\n",
		time.Now().Format("15:04:05"),
		cycleNum,
		requestID,
		result.CardNumber[len(result.CardNumber)-8:], // 显示卡号后8位
		status,
		result.Duration.Round(time.Millisecond),
		result.StatusCode,
	)

	if !result.Success && result.Error != "" {
		fmt.Printf("    %s %s\n", colorRed("错误:"), result.Error)
	}
}

// printCycleProgressWithTiming 显示循环测试进度（带时间统计）
func printCycleProgressWithTiming(requestID int, cycleNum int, result TestResult) {
	status := colorRed("FAIL")
	if result.Success {
		status = colorGreen("OK")
	}

	// 计算从goroutine启动到实际开始执行的延迟
	var delayInfo string
	if !result.GoroutineStartTime.IsZero() && !result.ActualStartTime.IsZero() {
		delay := result.ActualStartTime.Sub(result.GoroutineStartTime)
		if delay > time.Millisecond {
			delayInfo = fmt.Sprintf(" | 启动延迟: %v", delay.Round(time.Millisecond))
		}
	}

	fmt.Printf("[%s] 第%d轮 请求 #%d | 卡号: %s | 状态: %s | 耗时: %v | HTTP: %d%s\n",
		result.ActualStartTime.Format("15:04:05.000"), // 显示毫秒级时间
		cycleNum,
		requestID,
		result.CardNumber[len(result.CardNumber)-8:], // 显示卡号后8位
		status,
		result.Duration.Round(time.Millisecond),
		result.StatusCode,
		delayInfo,
	)

	if !result.Success && result.Error != "" {
		fmt.Printf("    %s %s\n", colorRed("错误:"), result.Error)
	}
}

// printTestConfig 显示测试配置
func printTestConfig(config TestConfig) {
	fmt.Printf("%s 测试配置:\n", colorCyan("📋"))
	fmt.Printf("  API地址: %s\n", config.BaseURL)
	fmt.Printf("  商户代码: %s\n", config.MerchantCode)
	fmt.Printf("  并发数: %d\n", config.Concurrency)
	if config.Duration > 0 {
		fmt.Printf("  测试时长: %d秒\n", config.Duration)
	} else {
		fmt.Printf("  请求总数: %d\n", config.TotalRequests)
	}
	fmt.Printf("  请求超时: %d秒\n", config.Timeout)
	fmt.Printf("  调试模式: %v\n", config.Debug)
	fmt.Println()
}

// analyzeResults 分析测试结果
func analyzeResults(results []TestResult, concurrency int, startTime, endTime time.Time) TestSummary {
	summary := TestSummary{
		TotalRequests: len(results),
		Concurrency:   concurrency,
		StartTime:     startTime,
		EndTime:       endTime,
		TotalDuration: endTime.Sub(startTime),
	}

	if len(results) == 0 {
		return summary
	}

	var totalDuration time.Duration
	summary.MinResponseTime = results[0].Duration
	summary.MaxResponseTime = results[0].Duration

	for _, result := range results {
		if result.Success {
			summary.SuccessRequests++
		} else {
			summary.FailedRequests++
		}

		totalDuration += result.Duration

		if result.Duration < summary.MinResponseTime {
			summary.MinResponseTime = result.Duration
		}
		if result.Duration > summary.MaxResponseTime {
			summary.MaxResponseTime = result.Duration
		}
	}

	summary.SuccessRate = float64(summary.SuccessRequests) / float64(summary.TotalRequests) * 100
	summary.AvgResponseTime = totalDuration / time.Duration(len(results))
	summary.QPS = float64(summary.TotalRequests) / summary.TotalDuration.Seconds()

	return summary
}

// printTestResults 显示测试结果
func printTestResults(summary TestSummary, results []TestResult) {
	fmt.Println()
	fmt.Println(colorBlue("📊 测试结果报告"))
	fmt.Println(colorBlue(strings.Repeat("=", 60)))

	// 基本统计
	fmt.Printf("%s 基本统计:\n", colorCyan("📈"))
	fmt.Printf("  总请求数: %d\n", summary.TotalRequests)
	fmt.Printf("  成功请求: %s\n", colorGreen(fmt.Sprintf("%d", summary.SuccessRequests)))
	fmt.Printf("  失败请求: %s\n", colorRed(fmt.Sprintf("%d", summary.FailedRequests)))
	fmt.Printf("  成功率: %s\n", getSuccessRateColor(summary.SuccessRate))
	fmt.Printf("  并发数: %d\n", summary.Concurrency)
	fmt.Println()

	// 性能统计
	fmt.Printf("%s 性能统计:\n", colorCyan("⚡"))
	fmt.Printf("  总耗时: %v\n", summary.TotalDuration.Round(time.Millisecond))
	fmt.Printf("  平均响应时间: %v\n", summary.AvgResponseTime.Round(time.Millisecond))
	fmt.Printf("  最快响应时间: %v\n", summary.MinResponseTime.Round(time.Millisecond))
	fmt.Printf("  最慢响应时间: %v\n", summary.MaxResponseTime.Round(time.Millisecond))
	fmt.Printf("  QPS (每秒请求数): %.2f\n", summary.QPS)
	fmt.Println()

	// 错误分析
	if summary.FailedRequests > 0 {
		printErrorAnalysis(results)
	}

	// 性能评估
	printPerformanceAssessment(summary)

	// 保存详细结果到文件
	saveResultsToFile(summary, results)
}

// getSuccessRateColor 根据成功率返回带颜色的字符串
func getSuccessRateColor(rate float64) string {
	rateStr := fmt.Sprintf("%.2f%%", rate)
	if rate >= 95 {
		return colorGreen(rateStr)
	} else if rate >= 80 {
		return colorYellow(rateStr)
	} else {
		return colorRed(rateStr)
	}
}

// printErrorAnalysis 显示错误分析
func printErrorAnalysis(results []TestResult) {
	fmt.Printf("%s 错误分析:\n", colorRed("❌"))

	errorCounts := make(map[string]int)
	statusCounts := make(map[int]int)

	for _, result := range results {
		if !result.Success {
			if result.Error != "" {
				errorCounts[result.Error]++
			}
			statusCounts[result.StatusCode]++
		}
	}

	// 显示HTTP状态码统计
	fmt.Printf("  HTTP状态码分布:\n")
	for status, count := range statusCounts {
		fmt.Printf("    %d: %d次\n", status, count)
	}

	// 显示错误类型统计
	if len(errorCounts) > 0 {
		fmt.Printf("  错误类型分布:\n")
		for errMsg, count := range errorCounts {
			fmt.Printf("    %s: %d次\n", errMsg, count)
		}
	}
	fmt.Println()
}

// printPerformanceAssessment 显示性能评估
func printPerformanceAssessment(summary TestSummary) {
	fmt.Printf("%s 性能评估:\n", colorCyan("🎯"))

	// 并发处理能力评估
	if summary.SuccessRate >= 95 && summary.AvgResponseTime <= 5*time.Second {
		fmt.Printf("  并发处理能力: %s (优秀)\n", colorGreen("✅"))
		fmt.Printf("    系统能够稳定处理 %d 个并发请求\n", summary.Concurrency)
	} else if summary.SuccessRate >= 80 && summary.AvgResponseTime <= 10*time.Second {
		fmt.Printf("  并发处理能力: %s (良好)\n", colorYellow("⚠️"))
		fmt.Printf("    系统基本能够处理 %d 个并发请求，但有改进空间\n", summary.Concurrency)
	} else {
		fmt.Printf("  并发处理能力: %s (需要改进)\n", colorRed("❌"))
		fmt.Printf("    系统在 %d 个并发请求下表现不佳\n", summary.Concurrency)
	}

	// 响应时间评估
	if summary.AvgResponseTime <= 1*time.Second {
		fmt.Printf("  响应时间: %s (非常快)\n", colorGreen("🚀"))
	} else if summary.AvgResponseTime <= 3*time.Second {
		fmt.Printf("  响应时间: %s (快)\n", colorGreen("⚡"))
	} else if summary.AvgResponseTime <= 5*time.Second {
		fmt.Printf("  响应时间: %s (一般)\n", colorYellow("⏱️"))
	} else {
		fmt.Printf("  响应时间: %s (慢)\n", colorRed("🐌"))
	}

	// QPS评估
	if summary.QPS >= 50 {
		fmt.Printf("  吞吐量: %s (高)\n", colorGreen("📈"))
	} else if summary.QPS >= 20 {
		fmt.Printf("  吞吐量: %s (中等)\n", colorYellow("📊"))
	} else {
		fmt.Printf("  吞吐量: %s (低)\n", colorRed("📉"))
	}

	// 系统稳定性评估
	if summary.SuccessRate >= 99 {
		fmt.Printf("  系统稳定性: %s (非常稳定)\n", colorGreen("🛡️"))
	} else if summary.SuccessRate >= 95 {
		fmt.Printf("  系统稳定性: %s (稳定)\n", colorGreen("✅"))
	} else if summary.SuccessRate >= 80 {
		fmt.Printf("  系统稳定性: %s (基本稳定)\n", colorYellow("⚠️"))
	} else {
		fmt.Printf("  系统稳定性: %s (不稳定)\n", colorRed("💥"))
	}

	fmt.Println()

	// 给出建议
	printRecommendations(summary)
}

// printRecommendations 显示建议
func printRecommendations(summary TestSummary) {
	fmt.Printf("%s 建议:\n", colorCyan("💡"))

	if summary.SuccessRate < 95 {
		fmt.Printf("  • %s 成功率偏低，建议检查:\n", colorYellow("⚠️"))
		fmt.Printf("    - 服务器资源使用情况\n")
		fmt.Printf("    - 数据库连接池配置\n")
		fmt.Printf("    - 应用程序错误日志\n")
	}

	if summary.AvgResponseTime > 5*time.Second {
		fmt.Printf("  • %s 响应时间较长，建议优化:\n", colorYellow("⚠️"))
		fmt.Printf("    - 数据库查询性能\n")
		fmt.Printf("    - 外部API调用超时设置\n")
		fmt.Printf("    - 代码中的阻塞操作\n")
	}

	if summary.QPS < 20 {
		fmt.Printf("  • %s 吞吐量较低，建议:\n", colorYellow("⚠️"))
		fmt.Printf("    - 增加服务器实例\n")
		fmt.Printf("    - 优化应用程序性能\n")
		fmt.Printf("    - 使用负载均衡\n")
	}

	if summary.SuccessRate >= 95 && summary.AvgResponseTime <= 3*time.Second {
		fmt.Printf("  • %s 系统表现良好！可以尝试:\n", colorGreen("✅"))
		fmt.Printf("    - 逐步增加并发数测试极限\n")
		fmt.Printf("    - 进行长时间稳定性测试\n")
		fmt.Printf("    - 监控生产环境性能指标\n")
	}

	fmt.Println()
}

// saveResultsToFile 保存结果到文件
func saveResultsToFile(summary TestSummary, results []TestResult) {
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("test_results_%s.json", timestamp)

	data := map[string]interface{}{
		"summary": summary,
		"results": results,
	}

	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		fmt.Printf("%s 保存结果文件失败: %v\n", colorRed("❌"), err)
		return
	}

	err = os.WriteFile(filename, jsonData, 0644)
	if err != nil {
		fmt.Printf("%s 写入结果文件失败: %v\n", colorRed("❌"), err)
		return
	}

	fmt.Printf("%s 详细结果已保存到: %s\n", colorGreen("💾"), filename)
}

// analyzeConcurrency 分析并发性
func analyzeConcurrency(results []TestResult, cycle int, expectedConcurrency int) {
	if len(results) == 0 {
		return
	}

	// 找到本轮的结果（简单实现：取最后N个结果）
	cycleResults := results
	if len(results) > expectedConcurrency {
		cycleResults = results[len(results)-expectedConcurrency:]
	}

	// 统计时间分布
	var startTimes []time.Time
	var delays []time.Duration

	for _, result := range cycleResults {
		if !result.ActualStartTime.IsZero() {
			startTimes = append(startTimes, result.ActualStartTime)
		}
		if result.WaitDuration > 0 {
			delays = append(delays, result.WaitDuration)
		}
	}

	if len(startTimes) < 2 {
		return
	}

	// 计算时间跨度
	var minTime, maxTime time.Time
	minTime = startTimes[0]
	maxTime = startTimes[0]

	for _, t := range startTimes {
		if t.Before(minTime) {
			minTime = t
		}
		if t.After(maxTime) {
			maxTime = t
		}
	}

	timeSpan := maxTime.Sub(minTime)

	// 分析并发性
	fmt.Printf("    %s 并发性分析:\n", colorCyan("🔍"))
	fmt.Printf("      启动时间跨度: %v\n", timeSpan.Round(time.Millisecond))

	if timeSpan < 100*time.Millisecond {
		fmt.Printf("      并发性评估: %s (所有请求在100ms内启动)\n", colorGreen("优秀"))
	} else if timeSpan < 500*time.Millisecond {
		fmt.Printf("      并发性评估: %s (所有请求在500ms内启动)\n", colorYellow("良好"))
	} else {
		fmt.Printf("      并发性评估: %s (启动时间跨度过大，可能存在串行化)\n", colorRed("需要改进"))
	}

	// 分析等待时间
	if len(delays) > 0 {
		var totalDelay time.Duration
		var maxDelay time.Duration
		for _, delay := range delays {
			totalDelay += delay
			if delay > maxDelay {
				maxDelay = delay
			}
		}
		avgDelay := totalDelay / time.Duration(len(delays))

		fmt.Printf("      平均启动延迟: %v\n", avgDelay.Round(time.Millisecond))
		fmt.Printf("      最大启动延迟: %v\n", maxDelay.Round(time.Millisecond))
	}
}
